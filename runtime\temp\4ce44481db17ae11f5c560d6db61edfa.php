<?php /*a:1:{s:82:"/www/wwwroot/www.lotteup.com/application/manage/view/withdrawal_channel/index.html";i:1753982761;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>代付渠道管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="withdrawal_channel" lay-filter="withdrawal_channel"></table>
                </div>
            </div>
        </div>
    </div>
    <!-- 头部左侧工具栏 -->
    <script type="text/html" id="toolbarDemo">
        <div class="layui-btn-container layui-btn-group">
            <button type="button" class="layui-btn layui-btn-sm" lay-event="add">
                <i class="layui-icon">&#xe654;</i> 添加代付渠道
            </button>
        </div>
    </script>
    <!-- 表单元素 -->
    <script type="text/html" id="state">
        <input type="checkbox" name="state" value="{{d.id}}" lay-skin="switch" lay-text="开|关" lay-filter="withdrawal-channel-status" {{ d.state == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="action">
        <div class="layui-btn-group">
            <button type="button" class="layui-btn layui-btn-xs" lay-event="edit">
                <i class="layui-icon">&#xe642;</i>
            </button>
            <button type="button" class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">
                <i class="layui-icon">&#xe640;</i>
            </button>
        </div>
    </script>
<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
<script>
    // 定义全局函数
    window.withdrawalEdit = function(obj,id,func,title,w='90%',h='90%'){
        layui.layer.open({
            type: 2,
            area: [w,h],
            title: title,
            content: '/manage/withdrawal_channel/'+func+'?id='+id
        });
    }

    window.withdrawalAdd = function(obj,func,title,w='90%',h='90%'){
        layui.layer.open({
            type: 2,
            area: [w,h],
            title: title,
            content: '/manage/withdrawal_channel/'+func
        });
    }

    layui.use(['table', 'form', 'layer'], function(){
        var $ = layui.$
        ,table = layui.table
        ,form = layui.form
        ,layer = layui.layer;

        //方法级渲染
        table.render({
            elem: '#withdrawal_channel'
            ,title: '代付渠道管理'
            ,url: '/manage/withdrawal_channel/index'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true}
                ,{field: 'name', title: '渠道名称', sort: true, fixed: 'left'}
                ,{field: 'mode', title: '代付模式', sort: true, templet: function(d){
                    switch(d.mode) {
                        case 'traditional': return '传统代付';
                        case 'watchPay': return 'WatchPay';
                        case 'jaya_pay': return 'JayaPay';
                        default: return '未知类型';
                    }
                }}
                ,{field: 'gateway', title: '代付网关', templet: function(d){
                    if(d.gateway === '无') {
                        return '<span style="color: #999;">无</span>';
                    }
                    return d.gateway;
                }}
                ,{field: 'sort', title: '排序', sort: true}
                ,{field: 'amount_range', title: '金额范围', templet: function(d){
                    return d.min_amount + ' - ' + d.max_amount;
                }}
                ,{field: 'fee_rate', title: '手续费率', sort: true, templet: function(d){
                    return d.fee_rate + '%';
                }}
                ,{field: 'state', title: '状态', sort: true, templet: '#state', unresize: true}
                ,{field: 'create_time', title: '创建时间', sort: true}
                ,{title: '操作', width: '20%', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            ,toolbar: '#toolbarDemo'
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
        });

        //监听排序事件
        table.on('sort(withdrawal_channel)', function(obj){
            table.reload('withdrawal_channel', {
                initSort: obj
                ,where: {
                    field: obj.field //排序字段
                    ,order: obj.type //排序方式
                }
            });
        });

        //监听工具栏事件
        table.on('toolbar(withdrawal_channel)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'add':
                    withdrawalAdd(this,'add','添加代付渠道','90%','90%');
                break;
            };
        });

        //监听行工具事件
        table.on('tool(withdrawal_channel)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('确定要删除该渠道吗？删除后不可恢复！', function(index){
                    $.post('/manage/withdrawal_channel/del', {id: data.id}, function(res){
                        if(res.code == 1){
                            obj.del();
                            layer.close(index);
                            layer.msg(res.msg, {icon: 1});
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                });
            } else if(obj.event === 'edit'){
                withdrawalEdit(this, data.id, 'edit', '编辑代付渠道', '90%', '90%');
            }
        });

        //监听开关状态切换
        form.on('switch(withdrawal-channel-status)', function(obj){
            var id = this.value;
            var state = obj.elem.checked ? 1 : 2;
            var loadIndex = layer.load(2);

            $.post('/manage/withdrawal_channel/changeState', {
                id: id,
                state: state
            }, function(res){
                layer.close(loadIndex);
                if(res.code == 1){
                    layer.msg(res.msg, {icon: 1});
                    // 重新获取代付渠道列表，刷新其他页面的渠道选择
                    $.get('/manage/withdrawal_channel/getEnabledChannels', function(channelRes) {
                        console.log('代付渠道列表已刷新:', channelRes);
                    }, 'json');
                }else{
                    layer.msg(res.msg, {icon: 2});
                    // 如果失败，恢复开关状态
                    obj.elem.checked = !obj.elem.checked;
                    form.render('checkbox');
                }
            }, 'json');
        });
    });
</script>
<script>
// 删除渠道
function delChannel(id){
    layui.use(['layer', 'jquery'], function(){
        var layer = layui.layer;
        var $ = layui.jquery;

        layer.confirm('确定要删除该渠道吗？删除后不可恢复！', {icon: 3, title:'提示'}, function(index){
            $.post('/manage/withdrawal_channel/del', {id: id}, function(res){
                if(res.code == 1){
                    layer.msg(res.msg, {icon: 1});
                    location.reload();
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
            layer.close(index);
        });
    });
}
</script>
</body>
</html>
