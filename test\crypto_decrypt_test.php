<?php
/**
 * 加密解密测试脚本
 * 模拟前端发送加密数据，测试后端解密流程
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../application/common/service/CryptoService.php';

use app\common\service\CryptoService;

echo "=== 加密解密测试 ===\n\n";

try {
    // 1. 获取RSA公钥
    echo "1. 获取RSA公钥...\n";
    $publicKey = CryptoService::x4(); // getPublicKey
    echo "公钥获取成功\n\n";
    
    // 2. 模拟前端加密过程
    echo "2. 模拟前端加密过程...\n";
    
    // 生成AES密钥和IV
    $aesKey = openssl_random_pseudo_bytes(32);
    $iv = openssl_random_pseudo_bytes(16);
    $salt = bin2hex(openssl_random_pseudo_bytes(16));
    
    echo "AES密钥长度: " . strlen($aesKey) . "\n";
    echo "IV长度: " . strlen($iv) . "\n";
    echo "盐值: " . $salt . "\n";
    
    // 准备要加密的数据
    $originalData = [
        'username' => 'testuser',
        'password' => 'testpass123',
        'timestamp' => time()
    ];
    
    // 构造加密前的数据结构
    $dataToEncrypt = [
        'salt' => $salt,
        'payload' => $originalData
    ];
    
    echo "原始数据: " . json_encode($originalData) . "\n";
    
    // AES加密数据
    $encryptedData = openssl_encrypt(
        json_encode($dataToEncrypt),
        'AES-256-CBC',
        $aesKey,
        OPENSSL_RAW_DATA,
        $iv
    );
    
    if (!$encryptedData) {
        throw new Exception('AES加密失败');
    }
    
    // RSA加密AES密钥
    $keyResource = openssl_pkey_get_public($publicKey);
    if (!$keyResource) {
        throw new Exception('RSA公钥无效');
    }
    
    $encryptedAesKey = '';
    $result = openssl_public_encrypt($aesKey, $encryptedAesKey, $keyResource);
    openssl_free_key($keyResource);
    
    if (!$result) {
        throw new Exception('RSA加密AES密钥失败');
    }
    
    // 生成盐值签名
    $saltSignature = CryptoService::x6($salt); // generateSaltSignature
    
    echo "加密完成\n\n";
    
    // 3. 构造请求数据（模拟前端发送的数据）
    echo "3. 构造加密请求数据...\n";
    
    $requestData = [
        'key' => base64_encode($encryptedAesKey),
        'data' => base64_encode($encryptedData),
        'iv' => base64_encode($iv),
        'salt_signature' => $saltSignature
    ];
    
    echo "请求数据字段: " . implode(', ', array_keys($requestData)) . "\n";
    echo "加密密钥长度: " . strlen($requestData['key']) . "\n";
    echo "加密数据长度: " . strlen($requestData['data']) . "\n\n";
    
    // 4. 测试后端解密
    echo "4. 开始后端解密测试...\n";
    echo "==========================================\n";
    
    $result = CryptoService::x1($requestData); // decryptRequest
    
    echo "==========================================\n";
    echo "解密测试完成\n\n";
    
    // 5. 验证结果
    echo "5. 验证解密结果...\n";
    
    if ($result['success']) {
        echo "✅ 解密成功!\n";
        echo "解密后的数据: " . json_encode($result['data']) . "\n";
        echo "盐值: " . $result['salt'] . "\n";
        
        // 验证数据完整性
        if ($result['data'] === $originalData) {
            echo "✅ 数据完整性验证通过!\n";
        } else {
            echo "❌ 数据完整性验证失败!\n";
            echo "原始数据: " . json_encode($originalData) . "\n";
            echo "解密数据: " . json_encode($result['data']) . "\n";
        }
    } else {
        echo "❌ 解密失败: " . $result['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试结束 ===\n";
