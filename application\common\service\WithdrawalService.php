<?php
namespace app\common\service;

use think\facade\Log;
use app\common\constants\PaymentStatus;
use app\common\constants\TradeType;

class WithdrawalService
{
    /**
     * 统一代付接口 - 与充值系统保持一致的架构
     */
    public function processWithdrawal($withdrawalData, $channelId)
    {
        try {
            // 根据渠道ID获取具体的渠道配置 - 与充值系统保持一致
            $channel = model('WithdrawalChannel')->where('id', $channelId)->where('state', 1)->find();
            if (!$channel) {
                return ['code' => 0, 'msg' => '代付渠道不存在或已禁用'];
            }

            // 验证代付金额范围
            $amount = floatval($withdrawalData['price']);
            if ($amount < $channel['min_amount'] || $amount > $channel['max_amount']) {
                return ['code' => 0, 'msg' => '代付金额超出渠道限制范围'];
            }



            // 记录代付请求日志
            Log::info('Withdrawal request: ' . json_encode([
                'withdrawal_id' => $withdrawalData['id'],
                'channel_id' => $channelId,
                'channel_mode' => $channel['mode'],
                'amount' => $amount,
                'card_number' => $withdrawalData['card_number']
            ]));

            // 根据渠道类型分流处理 - 与充值系统保持一致
            switch ($channel['mode']) {
                case 'watchPay':
                    return $this->processWatchPayWithdrawal($withdrawalData, $channel);

                case 'jaya_pay':
                    return $this->processJayaPayWithdrawal($withdrawalData, $channel);

                case 'traditional':
                    return $this->processTraditionalWithdrawal($withdrawalData, $channel);

                default:
                    return ['code' => 0, 'msg' => '不支持的代付模式'];
            }

        } catch (\Exception $e) {
            Log::error('Withdrawal processing error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '处理异常：' . $e->getMessage()];
        }
    }

    /**
     * 处理WatchPay代付
     */
    private function processWatchPayWithdrawal($withdrawalData, $channel)
    {
        try {
            // 从配置文件读取WatchPay配置
            $watchPayConfig = $this->getWatchPayConfig();
            if (!$watchPayConfig) {
                return ['code' => 0, 'msg' => 'WatchPay渠道配置不完整'];
            }

            // 构建WatchPay代付请求参数 - 按照官方文档格式
            $params = [
                'mch_id' => $watchPayConfig['merchant_id'],
                'mch_transferId' => $withdrawalData['order_number'],
                'transfer_amount' => (string)intval(floatval($withdrawalData['price'])), // WatchPay要求整数格式
                'apply_date' => date('Y-m-d H:i:s'),
                'bank_code' => (new TransactionService())->getBankCodeFromBankId($withdrawalData['bank_id'], 'watchpay'),
                'receive_name' => $withdrawalData['card_name'],
                'receive_account' => $withdrawalData['card_number'],
                'back_url' => $this->getWithdrawalNotifyUrl($channel),
                'sign_type' => 'MD5'
            ];

            // 生成签名
            $params['sign'] = $this->generateWatchPaySign($params, $watchPayConfig['secret_key']);

            // 发送WatchPay代付请求 - 使用新的API地址
            $withdrawalUrl = 'https://api.watchglb.com/pay/transfer';
            $response = $this->sendWatchPayRequest($withdrawalUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['respCode']) && $result['respCode'] == 'SUCCESS') {
                    Log::info('WatchPay withdrawal success: ' . $response);

                    // 处理同步回调状态
                    $tradeResult = $result['tradeResult'] ?? '';
                    if ($tradeResult === '1') {
                        // 同步返回转账成功，立即处理成功状态
                        $withdrawal = model('UserWithdrawals')->where('order_number', $withdrawalData['order_number'])->find();
                        if ($withdrawal) {
                            $this->processWithdrawalSuccess($withdrawal, $withdrawalData['price']);
                            Log::info("WatchPay withdrawal sync success processed: {$withdrawalData['order_number']}");
                            return ['code' => 1, 'msg' => '代付成功', 'data' => $result];
                        }
                    } elseif ($tradeResult === '2' || $tradeResult === '3') {
                        // 同步返回转账失败或拒绝，立即处理失败状态
                        $withdrawal = model('UserWithdrawals')->where('order_number', $withdrawalData['order_number'])->find();
                        if ($withdrawal) {
                            $failureReason = $tradeResult === '2' ? '转账失败' : '转账拒绝';
                            $this->processWithdrawalFailed($withdrawal, 'WatchPay代付失败：' . $failureReason);
                            Log::info("WatchPay withdrawal sync failed processed: {$withdrawalData['order_number']}, reason: {$failureReason}");
                            return ['code' => 0, 'msg' => 'WatchPay代付失败：' . $failureReason];
                        }
                    } else {
                        // 其他状态（0=申请成功，4=处理中），等待异步回调
                        Log::info("WatchPay withdrawal sync status {$tradeResult}, waiting for async callback: {$withdrawalData['order_number']}");
                    }

                    return ['code' => 1, 'msg' => '代付请求已提交', 'data' => $result];
                } else {
                    // 代付请求失败
                    $errorMsg = $result['errorMsg'] ?? '代付请求失败';
                    Log::error('WatchPay withdrawal failed: ' . $response);
                    return ['code' => 0, 'msg' => $errorMsg]; // 直接返回原始错误信息
                }
            } else {
                return ['code' => 0, 'msg' => '接口请求失败'];
            }

        } catch (\Exception $e) {
            Log::error('WatchPay withdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'WatchPay代付异常：' . $e->getMessage()];
        }
    }

    /**
     * 处理JayaPay代付
     */
    private function processJayaPayWithdrawal($withdrawalData, $channel)
    {
        try {
            // 从配置文件读取JayaPay配置
            $jayaPayConfig = $this->getJayaPayConfig();
            if (!$jayaPayConfig) {
                return ['code' => 0, 'msg' => 'JayaPay渠道配置不完整'];
            }

            // 构建JayaPay代付请求参数 - 按照官方文档格式
            $params = [
                'merchantCode' => $jayaPayConfig['merchant_id'],
                'orderType' => '0', // 法币交易
                'method' => 'Transfer', // 代付方式
                'orderNum' => $withdrawalData['order_number'],
                'money' => (string)intval(floatval($withdrawalData['price'])), // JayaPay要求整数格式，不能有小数点
                'currency' => 'IDR',
                'feeType' => '1', // 手续费另计
                'bankCode' => $jayaBankCode = (new TransactionService())->getBankCodeFromBankId($withdrawalData['bank_id'], 'jayapay'),
                'number' => $withdrawalData['card_number'],
                'name' => $withdrawalData['card_name'],
                'mobile' => '************', // 默认手机号，实际应从用户信息获取
                'email' => '<EMAIL>', // 默认邮箱，实际应从用户信息获取
                'notifyUrl' => $this->getWithdrawalNotifyUrl($channel),
                'dateTime' => date('YmdHis'),
                'description' => '代付下单'
            ];

            // 打印JayaPay代付银行编码日志
            Log::info('WithdrawalService JayaPay代付银行编码: ' . $jayaBankCode . ', bank_id: ' . $withdrawalData['bank_id'] . ', 卡号: ' . $withdrawalData['card_number']);

            // 生成JayaPay RSA签名
            $params['sign'] = $this->generateJayaPaySign($params, $jayaPayConfig['private_key']);

            // 发送JayaPay代付请求 - 使用JSON格式
            $withdrawalUrl = $jayaPayConfig['gateway_url'] ?: $this->getJayaPayWithdrawalUrl();
            $response = $this->sendJayaPayRequest($withdrawalUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['platRespCode']) && $result['platRespCode'] == 'SUCCESS') {
                    Log::info('JayaPay withdrawal success: ' . $response);

                    // 处理同步回调状态
                    $status = $result['status'] ?? '';
                    if ($status === '2') {
                        // 同步返回代付成功，立即处理成功状态
                        $withdrawal = model('UserWithdrawals')->where('order_number', $withdrawalData['order_number'])->find();
                        if ($withdrawal) {
                            $this->processWithdrawalSuccess($withdrawal, $withdrawalData['price']);
                            Log::info("JayaPay withdrawal sync success processed: {$withdrawalData['order_number']}");
                            return ['code' => 1, 'msg' => '代付成功', 'data' => $result];
                        }
                    } elseif ($status === '4') {
                        // 同步返回代付失败，立即处理失败状态
                        $withdrawal = model('UserWithdrawals')->where('order_number', $withdrawalData['order_number'])->find();
                        if ($withdrawal) {
                            $this->processWithdrawalFailed($withdrawal, 'JayaPay代付失败，状态：' . $status);
                            Log::info("JayaPay withdrawal sync failed processed: {$withdrawalData['order_number']}");
                            return ['code' => 0, 'msg' => 'JayaPay代付失败，状态：' . $status];
                        }
                    } else {
                        // 其他状态（0=待处理，1=已受理，5=银行代付中），等待异步回调
                        Log::info("JayaPay withdrawal sync status {$status}, waiting for async callback: {$withdrawalData['order_number']}");
                    }

                    return ['code' => 1, 'msg' => '代付请求已提交', 'data' => $result];
                } else {
                    $errorMsg = $result['platRespMessage'] ?? '代付请求失败';
                    Log::error('JayaPay withdrawal failed: ' . $response);
                    return ['code' => 0, 'msg' => $errorMsg]; // 直接返回原始错误信息
                }
            } else {
                return ['code' => 0, 'msg' => '接口请求失败'];
            }

        } catch (\Exception $e) {
            Log::error('JayaPay withdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'JayaPay代付异常：' . $e->getMessage()];
        }
    }

    /**
     * 处理传统代付
     */
    private function processTraditionalWithdrawal($withdrawalData, $channel)
    {
        try {
            // 传统代付：直接处理为成功状态，无需调用第三方接口
            // 记录传统代付处理日志
            Log::info('Traditional withdrawal processing: ' . json_encode([
                'withdrawal_id' => $withdrawalData['id'],
                'order_number' => $withdrawalData['order_number'],
                'amount' => $withdrawalData['price'],
                'card_number' => $withdrawalData['card_number'],
                'card_name' => $withdrawalData['card_name']
            ]));

            // 直接调用成功处理逻辑
            $successResult = $this->processWithdrawalSuccess($withdrawalData, $withdrawalData['price']);

            if ($successResult) {
                Log::info('Traditional withdrawal completed successfully: ' . $withdrawalData['order_number']);
                return ['code' => 1, 'msg' => '传统代付处理成功'];
            } else {
                Log::error('Traditional withdrawal success processing failed: ' . $withdrawalData['order_number']);
                return ['code' => 0, 'msg' => '状态更新失败'];
            }

        } catch (\Exception $e) {
            Log::error('Traditional withdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '处理异常：' . $e->getMessage()];
        }
    }

    /**
     * 发送HTTP请求
     */
    private function sendHttpRequest($url, $params)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            return $response;
        }
        
        return false;
    }

    // 删除重复的generateWatchPaySign方法，保留下面更完整的版本

    /**
     * 生成JayaPay MD5签名（备用方法）
     */
    private function generateJayaPayMD5Sign($params, $secretKey)
    {
        // 移除sign参数
        unset($params['sign']);

        // 按键名排序
        ksort($params);

        // 构建签名字符串
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr .= 'key=' . $secretKey;

        return strtoupper(md5($signStr));
    }



    /**
     * 获取代付回调地址 - 统一使用 unifiedWithdrawalCallback
     */
    private function getWithdrawalNotifyUrl($channel)
    {
        // 传统代付没有回调，返回空字符串
        if ($channel['mode'] === 'traditional') {
            return '';
        }

        try {
            // 直接从配置文件获取统一回调地址
            $configPath = app()->getRootPath() . 'config/payment_config.php';
            $paymentConfig = include($configPath);

            $unifiedCallbackUrl = $paymentConfig['global']['unified_withdrawal_callback_url'] ?? null;
            if ($unifiedCallbackUrl) {
                return $unifiedCallbackUrl;
            }
        } catch (\Exception $e) {
            Log::error('Failed to get unified callback URL: ' . $e->getMessage());
        }

        // 兜底方案：自动检测当前服务器信息
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';

        return $protocol . '://' . $host . '/api/transaction/unifiedWithdrawalCallback';
    }

    /**
     * 获取WatchPay代付接口地址
     */
    private function getWatchPayWithdrawalUrl()
    {
        return 'https://api.watchglb.com/pay/transfer';
    }

    /**
     * 查询WatchPay代付状态
     */
    public function queryWatchPayTransfer($orderNumber)
    {
        try {
            // 从配置文件读取WatchPay配置
            $watchPayConfig = $this->getWatchPayConfig();
            if (!$watchPayConfig) {
                return ['code' => 0, 'msg' => 'WatchPay渠道配置不完整'];
            }

            // 构建查询参数
            $params = [
                'mch_id' => $watchPayConfig['merchant_id'],
                'mch_transferId' => $orderNumber,
                'sign_type' => 'MD5'
            ];

            // 生成签名
            $params['sign'] = $this->generateWatchPaySign($params, $watchPayConfig['secret_key']);

            // 发送查询请求
            $queryUrl = 'https://api.watchglb.com/query/transfer';
            $response = $this->sendWatchPayRequest($queryUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['respCode']) && $result['respCode'] === 'SUCCESS') {
                    return [
                        'code' => 1,
                        'msg' => '查询成功',
                        'data' => [
                            'mch_id' => $result['mchId'],
                            'mch_transfer_id' => $result['mchTransferId'],
                            'transfer_amount' => $result['transferAmount'],
                            'trade_result' => $result['tradeResult'], // 0申请成功 1转账成功 2转账失败 3转账拒绝 4处理中
                            'apply_date' => $result['applyDate'],
                            'trade_no' => $result['tradeNo']
                        ]
                    ];
                } else {
                    return [
                        'code' => 0,
                        'msg' => $result['errorMsg'] ?? 'WatchPay代付查询失败'
                    ];
                }
            } else {
                return ['code' => 0, 'msg' => 'WatchPay查询接口请求失败'];
            }

        } catch (\Exception $e) {
            Log::error('WatchPay query transfer error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'WatchPay代付查询异常：' . $e->getMessage()];
        }
    }

    /**
     * 查询WatchPay账户余额
     */
    public function queryWatchPayBalance()
    {
        try {
            // 从配置文件读取WatchPay配置
            $watchPayConfig = $this->getWatchPayConfig();
            if (!$watchPayConfig) {
                return ['code' => 0, 'msg' => 'WatchPay渠道配置不完整'];
            }

            // 构建查询参数
            $params = [
                'mch_id' => $watchPayConfig['merchant_id'],
                'sign_type' => 'MD5'
            ];

            // 生成签名
            $params['sign'] = $this->generateWatchPaySign($params, $watchPayConfig['secret_key']);

            // 发送查询请求
            $balanceUrl = 'https://api.watchglb.com/query/balance';
            $response = $this->sendWatchPayRequest($balanceUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['respCode']) && $result['respCode'] === 'SUCCESS') {
                    return [
                        'code' => 1,
                        'msg' => '查询成功',
                        'data' => [
                            'mch_id' => $result['mchId'],
                            'amount' => $result['amount'],                     // 总金额
                            'frozen_amount' => $result['frozenAmount'],        // 冻结金额
                            'available_amount' => $result['availableAmount']   // 可用金额
                        ]
                    ];
                } else {
                    return [
                        'code' => 0,
                        'msg' => $result['errorMsg'] ?? 'WatchPay余额查询失败'
                    ];
                }
            } else {
                return ['code' => 0, 'msg' => 'WatchPay余额查询接口请求失败'];
            }

        } catch (\Exception $e) {
            Log::error('WatchPay query balance error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'WatchPay余额查询异常：' . $e->getMessage()];
        }
    }

    /**
     * 获取JayaPay代付接口地址
     */
    private function getJayaPayWithdrawalUrl()
    {
        return config('jaya_pay.gateway_urls.cash_out', 'https://openapi.jayapayment.com/gateway/cash');
    }

    /**
     * 发送WatchPay请求 - 使用form格式
     */
    private function sendWatchPayRequest($url, $params)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200) {
                return $response;
            } else {
                Log::error("WatchPay request failed with HTTP code: {$httpCode}");
                return false;
            }

        } catch (\Exception $e) {
            Log::error('WatchPay request error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送JayaPay请求 - 使用JSON格式
     */
    private function sendJayaPayRequest($url, $params)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200) {
                return $response;
            } else {
                Log::error("JayaPay request failed with HTTP code: {$httpCode}");
                return false;
            }

        } catch (\Exception $e) {
            Log::error('JayaPay request error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 统一代付回调处理服务 - 与充值系统保持一致
     */
    public function handleWithdrawalNotify($params)
    {
        try {
            // 判断回调类型并验证签名
            $isJayaPay = isset($params['platOrderNum']) || isset($params['platRespCode']);
            $isWatchPay = isset($params['orderid']) || isset($params['trade_status']);

            if ($isJayaPay) {
                // JayaPay代付回调处理
                return $this->handleJayaPayWithdrawalNotify($params);
            } elseif ($isWatchPay) {
                // WatchPay代付回调处理
                return $this->handleWatchPayWithdrawalNotify($params);
            } else {
                // 传统代付没有回调，记录错误日志
                Log::error('Traditional withdrawal has no callback, invalid params: ' . json_encode($params));
                return false;
            }

        } catch (\Exception $e) {
            Log::error('WithdrawalService handleNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理JayaPay代付回调
     */
    private function handleJayaPayWithdrawalNotify($params)
    {
        try {
            $orderNo = $params['orderNum'] ?? '';
            $status = $params['status'] ?? '';
            $amount = $params['money'] ?? 0;

            if (empty($orderNo)) {
                Log::error('JayaPay withdrawal notify: missing order number');
                return false;
            }

            // 查找提现记录
            $withdrawal = model('UserWithdrawals')->where('order_number', $orderNo)->find();
            if (!$withdrawal) {
                Log::error("JayaPay withdrawal notify: order not found - {$orderNo}");
                return false;
            }

            // 检查订单状态 - JayaPay
            if ($withdrawal['state'] == PaymentStatus::WITHDRAWAL_PAID) {
                Log::info("JayaPay withdrawal notify: order already processed - {$orderNo}");
                return true;
            }

            // 验证金额
            $notifyAmount = floatval($amount);
            if (abs($withdrawal['price'] - $notifyAmount) > 0.01) {
                Log::error("JayaPay withdrawal notify: amount mismatch - order: {$withdrawal['price']}, notify: {$notifyAmount}");
                return false;
            }

            // 根据JayaPay状态处理订单
            if ($status == '2' || $status == 'success') {
                return $this->processWithdrawalSuccess($withdrawal, $notifyAmount);
            } else {
                return $this->processWithdrawalFailed($withdrawal, 'JayaPay代付失败，状态：' . $status);
            }

        } catch (\Exception $e) {
            Log::error('JayaPay withdrawal handleNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理WatchPay代付回调
     */
    private function handleWatchPayWithdrawalNotify($params)
    {
        try {
            $orderNo = $params['orderid'] ?? '';
            $status = $params['status'] ?? '';
            $amount = $params['amount'] ?? 0;

            if (empty($orderNo)) {
                Log::error('WatchPay withdrawal notify: missing order number');
                return false;
            }

            // 查找提现记录
            $withdrawal = model('UserWithdrawals')->where('order_number', $orderNo)->find();
            if (!$withdrawal) {
                Log::error("WatchPay withdrawal notify: order not found - {$orderNo}");
                return false;
            }

            // 检查订单状态 - WatchPay
            if ($withdrawal['state'] == PaymentStatus::WITHDRAWAL_PAID) {
                Log::info("WatchPay withdrawal notify: order already processed - {$orderNo}");
                return true;
            }

            // 验证金额
            $notifyAmount = floatval($amount);
            if (abs($withdrawal['price'] - $notifyAmount) > 0.01) {
                Log::error("WatchPay withdrawal notify: amount mismatch - order: {$withdrawal['price']}, notify: {$notifyAmount}");
                return false;
            }

            // 根据WatchPay状态处理订单
            if ($status == '1' || $status == 'success') {
                return $this->processWithdrawalSuccess($withdrawal, $notifyAmount);
            } else {
                return $this->processWithdrawalFailed($withdrawal, 'WatchPay代付失败，状态：' . $status);
            }

        } catch (\Exception $e) {
            Log::error('WatchPay withdrawal handleNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 注意：传统代付没有自动回调功能
     * 传统代付的状态更新需要通过管理后台的提现审核功能手动处理
     */

    /**
     * 处理提现成功
     */
    private function processWithdrawalSuccess($withdrawal, $amount)
    {
        try {
            // 开启事务
            model('UserWithdrawals')->startTrans();

            // 更新提现记录状态
            model('UserWithdrawals')->where('id', $withdrawal['id'])->update([
                'state' => PaymentStatus::WITHDRAWAL_PAID,
                'set_time' => time(),
                'remarks' => '代付成功'
            ]);

            // 更新用户流水记录
            model('TradeDetails')->where('order_number', $withdrawal['order_number'])->update([
                'state' => PaymentStatus::TRADE_SUCCESS
            ]);

            // 获取用户信息用于每日报表
            $userInfo = model('Users')->where('id', $withdrawal['uid'])->find();

            // 更新每日报表
            $reportFormArray = [
                'uid' => $withdrawal['uid'],
                'username' => $userInfo['username'] ?? '',
                'user_type' => $userInfo['user_type'] ?? 1,
                'type' => 2,
                'price' => $withdrawal['price'],
                'isadmin' => 0,
                'isdaily' => 1  // 确保进入每日报表
            ];
            model('UserDaily')->updateReportForm($reportFormArray);

            // 提交事务
            model('UserWithdrawals')->commit();

            Log::info("Withdrawal success: {$withdrawal['order_number']}, amount: {$amount}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();
            Log::error("Withdrawal success transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现失败
     */
    private function processWithdrawalFailed($withdrawal, $reason = '代付失败')
    {
        try {
            // 开启事务
            model('UserWithdrawals')->startTrans();

            // 更新提现记录状态
            model('UserWithdrawals')->where('id', $withdrawal['id'])->update([
                'state' => PaymentStatus::WITHDRAWAL_REJECTED,
                'set_time' => time(),
                'remarks' => $reason
            ]);

            // 退还用户余额（包含手续费，因为提现失败用户不应承担手续费）
            model('UserTotal')->where('uid', $withdrawal['uid'])->setInc('balance', $withdrawal['price'] + $withdrawal['fee']);

            // 添加资金流水
            $userTotal = model('UserTotal')->where('uid', $withdrawal['uid'])->find();
            model('UserTransaction')->insert([
                'uid' => $withdrawal['uid'],
                'type' => 1, // 退款
                'money' => $withdrawal['price'] + $withdrawal['fee'],
                'balance' => $userTotal['balance'],
                'remarks' => '提现失败退款：' . $withdrawal['order_number'],
                'time' => time()
            ]);

            // 添加TradeDetails流水记录（用于前端显示）
            $userInfo = model('Users')->where('id', $withdrawal['uid'])->find();
            $tradeDetailsArray = [
                'uid' => $withdrawal['uid'],
                'username' => $userInfo['username'] ?? '',
                'order_number' => $withdrawal['order_number'],
                'trade_number' => 'L' . trading_number(),
                'trade_type' => TradeType::WITHDRAWAL_REFUND, // 提现退款（收入类型）
                'trade_before_balance' => $userTotal['balance'] - ($withdrawal['price'] + $withdrawal['fee']),
                'trade_amount' => $withdrawal['price'] + $withdrawal['fee'],
                'account_balance' => $userTotal['balance'],
                'remarks' => '提现失败退款：' . $withdrawal['order_number'],
                'types' => 1,
                'isadmin' => 0
            ];
            model('common/TradeDetails')->tradeDetails($tradeDetailsArray);

            // 更新用户流水记录
            // 更新用户流水记录状态为失败
            model('TradeDetails')->where('order_number', $withdrawal['order_number'])->update([
                'state' => PaymentStatus::TRADE_FAILED
            ]);

            // 提交事务
            model('UserWithdrawals')->commit();

            Log::info("Withdrawal failed: {$withdrawal['order_number']}, reason: {$reason}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();
            Log::error("Withdrawal failed transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成WatchPay MD5签名 - 按照官方文档规则
     */
    private function generateWatchPaySign($params, $secretKey)
    {
        // 移除签名相关字段
        unset($params['sign'], $params['sign_type']);

        // 移除空值参数（空值不参与签名）
        $filteredParams = [];
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null && $value !== 0) {
                $filteredParams[$key] = $value;
            }
        }

        // 按键名ASCII码排序
        ksort($filteredParams);

        // 构建签名字符串 - 按照k=v&k=v格式
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= $key . '=' . $value . '&';
        }

        // 添加密钥
        $signStr .= 'key=' . $secretKey;

        // 生成MD5签名并转为小写
        $sign = strtolower(md5($signStr));

        // 记录签名调试信息
        Log::info('WatchPay sign debug - signStr: ' . $signStr);
        Log::info('WatchPay sign debug - generated sign: ' . $sign);

        return $sign;
    }

    /**
     * 生成JayaPay RSA签名 - 按照官方文档规则
     */
    private function generateJayaPaySign($params, $privateKey)
    {
        // 移除签名字段
        unset($params['sign']);

        // 按键名ASCII排序
        ksort($params);

        // 构建签名字符串 - 只取值拼接
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signStr .= $value;
            }
        }

        // 使用RSA私钥签名
        try {
            $privateKeyResource = openssl_pkey_get_private($privateKey);
            if (!$privateKeyResource) {
                throw new \Exception('Invalid private key');
            }

            openssl_sign($signStr, $signature, $privateKeyResource, OPENSSL_ALGO_SHA1);
            openssl_free_key($privateKeyResource);

            return base64_encode($signature);

        } catch (\Exception $e) {
            Log::error('JayaPay sign generation error: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 从配置文件获取WatchPay配置
     */
    private function getWatchPayConfig()
    {
        try {
            // 修复路径问题 - 使用相对路径获取根目录
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                Log::error('Payment config file not found');
                return false;
            }

            // 从payment_config.php读取WatchPay配置
            $paymentConfig = include($configPath);

            if (!isset($paymentConfig['watch_pay']) || !$paymentConfig['watch_pay']['enabled']) {
                return false;
            }

            // 获取印尼配置（默认国家）
            $countryConfig = $paymentConfig['watch_pay']['countries']['ID'] ?? null;
            if (!$countryConfig || !$countryConfig['enabled']) {
                return false;
            }

            $config = [
                'merchant_id' => $countryConfig['merchant_id'],
                'secret_key' => $countryConfig['withdrawal_key'] ?? $countryConfig['pay_key'], // 优先使用代付密钥
                'gateway_url' => $countryConfig['gateway_url'],
                'notify_url' => $countryConfig['notify_domain'],
            ];

            // 验证必要配置
            if (empty($config['merchant_id']) || empty($config['secret_key'])) {
                return false;
            }

            return $config;

        } catch (\Exception $e) {
            Log::error('getWatchPayConfig error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 从配置文件获取JayaPay配置
     */
    private function getJayaPayConfig()
    {
        try {
            // 修复路径问题 - 使用相对路径获取根目录
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                Log::error('Payment config file not found');
                return false;
            }

            // 从payment_config.php读取JayaPay配置
            $paymentConfig = include($configPath);

            if (!isset($paymentConfig['jaya_pay']) || !$paymentConfig['jaya_pay']['enabled']) {
                return false;
            }

            // 获取默认商户配置
            $merchantConfig = $paymentConfig['jaya_pay']['merchants']['default'] ?? null;
            if (!$merchantConfig || !$merchantConfig['enabled']) {
                return false;
            }

            $config = [
                'merchant_id' => $merchantConfig['merchant_code'],
                'secret_key' => $merchantConfig['private_key'], // JayaPay使用private_key作为secret_key
                'public_key' => $merchantConfig['public_key'],
                'private_key' => $merchantConfig['private_key'],
                'gateway_url' => $paymentConfig['jaya_pay']['gateway_urls']['cash_out'],
                'notify_url' => $merchantConfig['notify_url'],
            ];

            // 验证必要配置
            if (empty($config['merchant_id']) || empty($config['private_key']) ||
                empty($config['public_key'])) {
                return false;
            }

            return $config;

        } catch (\Exception $e) {
            Log::error('getJayaPayConfig error: ' . $e->getMessage());
            return false;
        }
    }
}
