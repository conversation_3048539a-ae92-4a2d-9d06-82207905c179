<?php
namespace app\api\model;

use app\common\model\WheelModel as CommonWheelModel;
use app\common\constants\TradeType;

class WheelModel extends CommonWheelModel{
    
    /**
     * 获取抽奖奖品配置
     * @return array
     */
    public function getPrizes(){
        $lang = (input('post.lang')) ? input('post.lang') : 'id'; // 语言类型
        
        try {
            // 获取奖品列表
            $prizes = $this->order('order asc')->select();
            
            // 获取抽奖配置
            $config = model('WheelConfig')->where('id', 1)->find();
            
            $prize_list = [];
            foreach ($prizes as $prize) {
                $prize_data = [
                    'id' => $prize['id'],
                    'rate' => $prize['rate'],
                    'type' => $prize['type'], // 0=谢谢参与，1=任务次数，2=现金奖励
                    'num' => $prize['num'], // 任务次数
                    'cash_amount' => $prize['cash_amount'], // 现金金额
                    'order' => $prize['order']
                ];
                
                // 根据语言返回对应的奖品名称
                switch($lang) {
                    case 'cn':
                        $prize_data['name'] = $prize['name_cn'];
                        break;
                    case 'en':
                        $prize_data['name'] = $prize['name'];
                        break;
                    case 'id':
                        $prize_data['name'] = $prize['name_id'];
                        break;
                    case 'ft':
                        $prize_data['name'] = $prize['name_hk'];
                        break;
                    case 'yd':
                        $prize_data['name'] = $prize['name_yd'];
                        break;
                    case 'vi':
                        $prize_data['name'] = $prize['name_vn'];
                        break;
                    case 'es':
                        $prize_data['name'] = $prize['name_es'];
                        break;
                    case 'ja':
                        $prize_data['name'] = $prize['name_jp'];
                        break;
                    case 'th':
                        $prize_data['name'] = $prize['name_th'];
                        break;
                    case 'ma':
                        $prize_data['name'] = $prize['name_ma'];
                        break;
                    case 'pt':
                        $prize_data['name'] = $prize['name_pt'];
                        break;
                    default:
                        $prize_data['name'] = $prize['name_id'];
                }
                
                $prize_list[] = $prize_data;
            }
            
            // 获取抽奖说明
            $remark = '';
            if ($config) {
                switch($lang) {
                    case 'cn':
                        $remark = $config['remark_cn'];
                        break;
                    case 'en':
                        $remark = $config['remark'];
                        break;
                    case 'id':
                        $remark = $config['remark_id'];
                        break;
                    case 'ft':
                        $remark = $config['remark_hk'];
                        break;
                    case 'yd':
                        $remark = $config['remark_yd'];
                        break;
                    case 'vi':
                        $remark = $config['remark_vn'];
                        break;
                    case 'es':
                        $remark = $config['remark_es'];
                        break;
                    case 'ja':
                        $remark = $config['remark_jp'];
                        break;
                    case 'th':
                        $remark = $config['remark_th'];
                        break;
                    case 'ma':
                        $remark = $config['remark_ma'];
                        break;
                    case 'pt':
                        $remark = $config['remark_pt'];
                        break;
                    default:
                        $remark = $config['remark_id'];
                }
            }
            
            return [
                'code' => 1,
                'prizes' => $prize_list,
                'config' => [
                    'remark' => $remark
                ]
            ];
            
        } catch (Exception $e) {
            return [
                'code' => 0,
                'msg' => 'Failed to get lottery prizes'
            ];
        }
    }

    /**
     * 获取用户剩余抽奖次数
     * @return array
     */
    public function getRemainingTimes(){
        $token = input('post.token/s');
        $lang = (input('post.lang')) ? input('post.lang') : 'id'; // 语言类型

        if (!$token) {
            return [
                'code' => 0,
                'msg' => 'Token is required'
            ];
        }

        try {
            $userArr = explode(',', auth_code($token, 'DECODE'));
            $uid = $userArr[0]; // uid

            // 获取用户VIP等级
            $user_level = model('Users')->where('id', $uid)->value('vip_level');
            if (!$user_level) {
                return [
                    'code' => 0,
                    'msg' => 'User not found'
                ];
            }

            // 获取VIP等级的每日基础抽奖次数
            $base_times = model('UserGrade')->where('grade', $user_level)->value('daily_turntable_times');
            $base_times = $base_times ?: 0;

            // 获取今日日期（时间戳）- 与UserVipModel保持一致
            $today = mktime(0, 0, 0, date('m'), date('d'), date('Y'));

            // 查询用户今日抽奖次数记录
            $lottery_record = model('UserLotteryTimes')->where([
                'uid' => $uid,
                'date' => $today
            ])->find();

            if ($lottery_record) {
                // 如果有记录，直接返回剩余次数
                $remaining_times = $lottery_record['remaining_times'];
                $used_times = $lottery_record['used_times'];
                $total_times = $lottery_record['base_times'] + $lottery_record['reward_times'];
            } else {
                // 如果没有记录，创建新记录
                $remaining_times = $base_times;
                $used_times = 0;
                $total_times = $base_times;

                // 创建今日抽奖次数记录
                if ($base_times > 0) {
                    model('UserLotteryTimes')->insert([
                        'uid' => $uid,
                        'date' => $today,
                        'base_times' => $base_times,
                        'reward_times' => 0,
                        'used_times' => 0,
                        'remaining_times' => $base_times,
                        'create_time' => time(),
                        'update_time' => time()
                    ]);
                }
            }

            return [
                'code' => 1,
                'data' => [
                    'remaining_times' => $remaining_times,
                    'used_times' => $used_times,
                    'total_times' => $total_times,
                    'base_times' => $base_times,
                    'reward_times' => $lottery_record ? $lottery_record['reward_times'] : 0
                ]
            ];

        } catch (Exception $e) {
            return [
                'code' => 0,
                'msg' => 'Failed to get remaining times'
            ];
        }
    }

    /**
     * 执行抽奖
     * @return array
     */
    public function draw(){
        $token = input('post.token/s');
        $lang = (input('post.lang')) ? input('post.lang') : 'id'; // 语言类型

        if (!$token) {
            return [
                'code' => 0,
                'msg' => 'Token is required'
            ];
        }

        try {
            $userArr = explode(',', auth_code($token, 'DECODE'));
            $uid = $userArr[0]; // uid

            // 获取用户VIP等级
            $user_level = model('Users')->where('id', $uid)->value('vip_level');
            if (!$user_level) {
                return [
                    'code' => 0,
                    'msg' => 'User not found'
                ];
            }

            // 获取今日日期（时间戳）- 与UserVipModel保持一致
            $today = mktime(0, 0, 0, date('m'), date('d'), date('Y'));

            // 查询用户今日抽奖次数记录
            $lottery_record = model('UserLotteryTimes')->where([
                'uid' => $uid,
                'date' => $today
            ])->find();

            if (!$lottery_record || $lottery_record['remaining_times'] <= 0) {
                return [
                    'code' => 0,
                    'msg' => $lang == 'cn' ? '今日抽奖次数已用完' : 'No lottery chances left today'
                ];
            }

            // 抽奖免费，无需检查余额和扣费
            $price = 0;

            // 获取奖品列表
            $prizes = $this->order('order asc')->select();
            $prize_arr = [];

            foreach ($prizes as $prize) {
                $prize_name = '';
                switch ($lang) {
                    case 'cn':
                        $prize_name = $prize['name_cn'];
                        break;
                    case 'en':
                        $prize_name = $prize['name'];
                        break;
                    case 'id':
                        $prize_name = $prize['name_id'];
                        break;
                    default:
                        $prize_name = $prize['name_id'];
                        break;
                }

                $prize_arr[] = [
                    'id' => $prize['id'],
                    'prize' => $prize_name,
                    'rate' => $prize['rate'],
                    'type' => $prize['type'],
                    'num' => $prize['num'],
                    'cash_amount' => $prize['cash_amount'],
                    'order' => $prize['order']
                ];
            }

            // 执行抽奖算法
            $rates = array_column($prize_arr, 'rate');
            $key = $this->get_rand($rates);
            $result = $prize_arr[$key];

            // 更新抽奖次数
            model('UserLotteryTimes')->where([
                'uid' => $uid,
                'date' => $today
            ])->inc('used_times', 1)
              ->setDec('remaining_times', 1)
              ->update(['update_time' => time()]);

            // 处理奖励
            $this->processReward($uid, $result);

            return [
                'code' => 1,
                'data' => $result
            ];

        } catch (Exception $e) {
            return [
                'code' => 0,
                'msg' => 'Draw failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 概率计算函数
     * @param array $proArr 概率数组
     * @return int 中奖索引
     */
    private function get_rand($proArr) {
        $result = '';
        $proSum = array_sum($proArr);
        foreach ($proArr as $key => $proCur) {
            $randNum = mt_rand(1, $proSum);
            if ($randNum <= $proCur) {
                $result = $key;
                break;
            } else {
                $proSum -= $proCur;
            }
        }
        unset($proArr);
        return $result;
    }

    /**
     * 处理奖励
     * @param int $uid 用户ID
     * @param array $prize 奖品信息
     */
    private function processReward($uid, $prize) {
        // 记录抽奖记录
        $record_data = [
            'user_id' => $uid,
            'wheel_id' => $prize['id'],
            'time' => time(),
            'wheel_name' => $prize['prize'], // 使用正确的字段名
            'end_time' => time() + 86400, // 24小时后过期
            'num' => $prize['num'],
            'amount' => 0,
            'cash_amount' => $prize['cash_amount'],
            'prize_type' => $prize['type']
        ];
        model('WheelRecord')->insert($record_data);

        // 处理不同类型的奖励
        if ($prize['type'] == 1 && $prize['num'] > 0) {
            // 任务次数奖励 - 这里可以添加任务次数处理逻辑
            // 暂时不处理，因为原系统中任务次数的处理比较复杂
        } elseif ($prize['type'] == 2 && $prize['cash_amount'] > 0) {
            // 现金奖励 - 获取用户当前余额
            $current_balance = model('UserTotal')->where('uid', $uid)->value('balance');

            // 给用户账户增加现金（同时更新余额和总资产）
            model('UserTotal')->where('uid', $uid)->inc('balance', $prize['cash_amount'])->inc('total_balance', $prize['cash_amount'])->update();

            // 获取更新后的余额
            $new_balance = model('UserTotal')->where('uid', $uid)->value('balance');

            // 使用 TradeDetails 模型记录资金流水
            $financial_data = [
                'uid' => $uid,
                'trade_type' => TradeType::WHEEL, // 大转盘
                'trade_amount' => $prize['cash_amount'],
                'trade_before_balance' => $current_balance,
                'account_balance' => $new_balance,
                'types' => 1, // 用户类型
                'state' => 1
            ];
            // 添加多语言备注
            $financial_data = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financial_data, 'lottery_cash_reward', [
                'prize' => $prize['prize']
            ]);
            model('common/TradeDetails')->tradeDetails($financial_data);
        }
    }

    /**
     * 获取个人中奖记录
     * @return array
     */
    public function getWinRecords(){
        $token = input('post.token');
        $page = input('post.page', 1); // 页码，默认第1页
        $limit = input('post.limit', 10); // 每页记录数，默认10条
        $lang = input('post.lang', 'id'); // 语言类型，默认印尼语

        if (!$token) {
            return [
                'code' => 0,
                'msg' => 'Token is required'
            ];
        }

        try {
            $userArr = explode(',', auth_code($token, 'DECODE'));
            $uid = $userArr[0]; // uid

            // 验证用户是否存在
            $user = model('Users')->where('id', $uid)->find();
            if (!$user) {
                return [
                    'code' => 0,
                    'msg' => 'User not found'
                ];
            }

            // 计算偏移量
            $offset = ($page - 1) * $limit;

            // 获取总记录数
            $total = model('WheelRecord')->where('user_id', $uid)->count();

            // 获取中奖记录列表
            $records = model('WheelRecord')
                ->where('user_id', $uid)
                ->order('time desc')
                ->limit($offset, $limit)
                ->select();

            $record_list = [];
            foreach ($records as $record) {
                // 根据奖品类型设置显示文本
                $prize_text = '';
                $prize_value = '';

                switch ($record['prize_type']) {
                    case 0:
                        $prize_text = $this->getLanguageText('谢谢参与', $lang);
                        $prize_value = '';
                        break;
                    case 1:
                        $prize_text = $this->getLanguageText('任务次数', $lang) . ' ' . $record['num'] . ' ' . $this->getLanguageText('次', $lang);
                        $prize_value = $record['num'] . ' ' . $this->getLanguageText('次', $lang);
                        break;
                    case 2:
                        $prize_text = $this->getLanguageText('现金奖励', $lang) . ' $' . $record['cash_amount'];
                        $prize_value = '$' . $record['cash_amount'];
                        break;
                }

                // 判断是否过期（仅对任务次数类型有效）
                $is_expired = false;
                if ($record['prize_type'] == 1 && $record['end_time'] > 0) {
                    $is_expired = time() > $record['end_time'];
                }

                $record_data = [
                    'id' => $record['id'],
                    'wheel_name' => $record['wheel_name'],
                    'prize_type' => $record['prize_type'],
                    'prize_text' => $prize_text,
                    'prize_value' => $prize_value,
                    'num' => $record['num'],
                    'cash_amount' => $record['cash_amount'],
                    'time' => $record['time'],
                    'time_formatted' => date('Y-m-d H:i:s', $record['time']),
                    'end_time' => $record['end_time'],
                    'end_time_formatted' => $record['end_time'] > 0 ? date('Y-m-d H:i:s', $record['end_time']) : '',
                    'is_expired' => $is_expired
                ];

                $record_list[] = $record_data;
            }

            // 计算分页信息
            $total_pages = ceil($total / $limit);
            $has_more = $page < $total_pages;

            return [
                'code' => 1,
                'data' => [
                    'records' => $record_list,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $total,
                        'total_pages' => $total_pages,
                        'has_more' => $has_more
                    ]
                ]
            ];

        } catch (Exception $e) {
            return [
                'code' => 0,
                'msg' => 'Failed to get records: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取多语言文本
     * @param string $text 中文文本
     * @param string $lang 语言代码
     * @return string
     */
    private function getLanguageText($text, $lang) {
        $translations = [
            '谢谢参与' => [
                'id' => 'Terima kasih telah berpartisipasi',
                'en' => 'Thank you for participating',
                'cn' => '谢谢参与',
                'th' => 'ขอบคุณที่เข้าร่วม',
                'vi' => 'Cảm ơn bạn đã tham gia',
                'ja' => 'ご参加ありがとうございます',
                'es' => 'Gracias por participar',
                'pt' => 'Obrigado por participar',
                'ma' => 'Terima kasih kerana mengambil bahagian'
            ],
            '任务次数' => [
                'id' => 'Jumlah tugas',
                'en' => 'Task times',
                'cn' => '任务次数',
                'th' => 'จำนวนงาน',
                'vi' => 'Số lần nhiệm vụ',
                'ja' => 'タスク回数',
                'es' => 'Veces de tarea',
                'pt' => 'Vezes de tarefa',
                'ma' => 'Bilangan tugas'
            ],
            '次' => [
                'id' => 'kali',
                'en' => 'times',
                'cn' => '次',
                'th' => 'ครั้ง',
                'vi' => 'lần',
                'ja' => '回',
                'es' => 'veces',
                'pt' => 'vezes',
                'ma' => 'kali'
            ],
            '现金奖励' => [
                'id' => 'Hadiah uang tunai',
                'en' => 'Cash reward',
                'cn' => '现金奖励',
                'th' => 'รางวัลเงินสด',
                'vi' => 'Phần thưởng tiền mặt',
                'ja' => '現金報酬',
                'es' => 'Recompensa en efectivo',
                'pt' => 'Recompensa em dinheiro',
                'ma' => 'Ganjaran tunai'
            ]
        ];

        if (isset($translations[$text][$lang])) {
            return $translations[$text][$lang];
        }

        // 如果没有找到对应语言，返回中文
        return $text;
    }
}
