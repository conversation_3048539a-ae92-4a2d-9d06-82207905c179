<?php
/**
 * 测试会话级解密功能
 */

// 模拟curl请求中的数据
$testData = [
    'encrypted' => 'true',
    'session_id' => '45d76ad6-bc07-4735-b5e7-db6c8a30a144',
    'data' => 'G5EUzR3lwEbgPU19y%2Bag9ROqaLJHRxCf63Hba7MQrxLt%2BsxdM98yPODEkulT3oXt86mW36h7pddTDPrTnoHRjtu%2FPvKZXcMKEghncAhnbYsLPNc2lSECy6xd9Yd%2BmPJISE86qKkQh1jg0DKoFrSSqu12HlaycGkA6BSXlFnqtfuIKp6%2FA%2BULETEwc0tAbDv7',
    'iv' => '6Xs9upVzD7jFSJCnf5Xu7w%3D%3D'
];

// URL解码
$testData['data'] = urldecode($testData['data']);
$testData['iv'] = urldecode($testData['iv']);

echo "=== 测试会话级解密 ===\n";
echo "原始数据:\n";
echo "encrypted: " . $testData['encrypted'] . "\n";
echo "session_id: " . $testData['session_id'] . "\n";
echo "data length: " . strlen($testData['data']) . "\n";
echo "iv length: " . strlen($testData['iv']) . "\n";
echo "data: " . substr($testData['data'], 0, 50) . "...\n";
echo "iv: " . $testData['iv'] . "\n\n";

// 加载ThinkPHP环境
require_once __DIR__ . '/thinkphp/start.php';

try {
    // 直接调用CryptoService的会话解密方法
    $result = \app\common\service\CryptoService::decryptWithSession(
        $testData['data'],
        $testData['iv'],
        $testData['session_id']
    );

    echo "解密结果:\n";
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

    if ($result['success']) {
        echo "\n解密成功！解密后的数据:\n";
        if (is_array($result['data'])) {
            foreach ($result['data'] as $key => $value) {
                echo "  $key: $value\n";
            }
        } else {
            echo "  " . $result['data'] . "\n";
        }
    } else {
        echo "\n解密失败: " . $result['error'] . "\n";
        if (isset($result['error_msg'])) {
            echo "详细信息: " . $result['error_msg'] . "\n";
        }
    }

} catch (Exception $e) {
    echo "测试过程中发生异常: " . $e->getMessage() . "\n";
    echo "异常堆栈:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";