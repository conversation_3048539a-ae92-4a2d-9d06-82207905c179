<?php
namespace app\common\middleware;

use app\common\service\CryptoService;
use think\facade\Log;

/**
 * 加解密中间件
 * 自动处理加密请求和响应
 */
class CryptoMiddleware
{
    /**
     * 处理请求
     * @param \think\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, \Closure $next)
    {
        // 检查是否为加密请求
        if ($this->isEncryptedRequest($request)) {
            // 解密请求数据
            $decryptResult = $this->decryptRequestData($request);
            
            if (!$decryptResult['success']) {
                return $this->errorResponse($decryptResult['error']);
            }
            
            // 将解密后的数据注入到请求中
            $request->withInput($decryptResult['data']);
            
            // 标记为已解密请求
            $request->isDecrypted = true;
            $request->originalSalt = $decryptResult['salt'];
        }
        
        // 继续处理请求
        $response = $next($request);
        
        // 检查是否需要加密响应
        if ($this->shouldEncryptResponse($request, $response)) {
            return $this->encryptResponse($response);
        }
        
        return $response;
    }
    
    /**
     * 检查是否为加密请求
     * @param \think\Request $request
     * @return bool
     */
    private function isEncryptedRequest($request)
    {
        // 检查请求头或参数中是否包含加密标识
        $contentType = $request->header('content-type');
        $encryptFlag = $request->header('X-Encrypt-Request');
        
        // 检查是否包含加密请求的必要字段
        $hasEncryptFields = $request->has('key') && 
                           $request->has('data') && 
                           $request->has('iv') && 
                           $request->has('salt_signature');
        
        return $encryptFlag === 'true' || $hasEncryptFields;
    }
    
    /**
     * 解密请求数据
     * @param \think\Request $request
     * @return array
     */
    private function decryptRequestData($request)
    {
        try {
            $requestData = [
                'key' => $request->param('key'),
                'data' => $request->param('data'),
                'iv' => $request->param('iv'),
                'salt_signature' => $request->param('salt_signature')
            ];
            
            // 使用混淆方法名
            return CryptoService::x1($requestData);
            
        } catch (\Exception $e) {
            Log::error('中间件解密请求失败: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => '请求解密失败'
            ];
        }
    }
    
    /**
     * 检查是否需要加密响应
     * @param \think\Request $request
     * @param mixed $response
     * @return bool
     */
    private function shouldEncryptResponse($request, $response)
    {
        // 如果请求是加密的，响应也应该加密
        if (isset($request->isDecrypted) && $request->isDecrypted) {
            return true;
        }
        
        // 检查响应头中的加密标识
        $encryptResponse = $request->header('X-Encrypt-Response');
        return $encryptResponse === 'true';
    }
    
    /**
     * 加密响应数据
     * @param mixed $response
     * @return \think\Response
     */
    private function encryptResponse($response)
    {
        try {
            // 获取响应数据
            $responseData = $response->getData();
            
            // 使用混淆方法名加密响应
            $encryptResult = CryptoService::x5($responseData);
            
            if (!$encryptResult) {
                throw new \Exception('响应加密失败');
            }
            
            // 返回加密后的响应
            return json([
                'encrypted' => true,
                'key' => $encryptResult['key'],
                'data' => $encryptResult['data'],
                'iv' => $encryptResult['iv'],
                'salt_signature' => $encryptResult['salt_signature']
            ]);
            
        } catch (\Exception $e) {
            Log::error('中间件加密响应失败: ' . $e->getMessage());
            return $this->errorResponse('响应加密失败');
        }
    }
    
    /**
     * 返回错误响应
     * @param string $message
     * @return \think\Response
     */
    private function errorResponse($message)
    {
        return json([
            'code' => 0,
            'msg' => $message,
            'data' => null
        ], 400);
    }
}
