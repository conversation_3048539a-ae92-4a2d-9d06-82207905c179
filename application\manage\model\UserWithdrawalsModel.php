<?php
namespace app\manage\model;

use think\Model;
use app\common\constants\PaymentStatus;
use app\common\constants\TradeType;

class UserWithdrawalsModel extends Model{
	//表名
	protected $table = 'ly_user_withdrawals';

	/**
	 * 提现记录
	 */
	public function withdrawalsList(){
		$param = input('get.');
		//查询条件组装
		$where = array();
		//分页参数组装
		$pageParam = array();
		// 状态搜索
		if (isset($param['isUser']) && $param['isUser'] == 1) $pageParam['isUser'] = $param['isUser'];
		//搜索类型
		if(isset($param['search_t']) && $param['search_t'] && isset($param['search_c']) && $param['search_c']){
			switch ($param['search_t']) {
				case 'username':
					$userId = model('Users')->where('username',$param['search_c'])->value('id');
					$where[] = array('ly_user_withdrawals.uid','=',$userId);
					break;
				case 'order_number':
					$where[] = array('order_number','=',$param['search_c']);
					break;
				case 'card_name':
					$where[] = array('card_name','=',$param['search_c']);
					break;
				case 'card_number':
					$where[] = array('card_number','=',$param['search_c']);
					break;
			}
			$pageParam['search_t'] = $param['search_t'];
			$pageParam['search_c'] = $param['search_c'];
		}
		
		//状态搜索
		if(isset($param['state']) && $param['state']){
			$where[] = array('ly_user_withdrawals.state','=',$param['state']);
			$pageParam['state'] = $param['state'];
		}
		// 时间
		if(isset($param['datetime_range']) && $param['datetime_range']){
			$dateTime = explode(' - ', $param['datetime_range']);
			$where[] = array('ly_user_withdrawals.time','>=',strtotime($dateTime[0]));
			$where[] = array('ly_user_withdrawals.time','<=',strtotime($dateTime[1]));
			$pageParam['datetime_range'] = $param['datetime_range'];
		}else{
			$todayStart = mktime(0,0,0,date('m'),date('d'),date('Y'));
			$where[] = array('ly_user_withdrawals.time','>=',$todayStart);
			$todayEnd = mktime(23,59,59,date('m'),date('d'),date('Y'));
			$where[] = array('ly_user_withdrawals.time','<=',$todayEnd);
		}

		//查询符合条件的数据
		$resultData = $this->field('ly_user_withdrawals.*,manage.username as aname,users.username,danger,bank.bank_name')->join('users','ly_user_withdrawals.uid = users.id')->join('manage','ly_user_withdrawals.aid = manage.id','left')->join('bank','ly_user_withdrawals.bank_id = bank.id','left')->where($where)->order('time','desc')->paginate(16,false,['query'=>$pageParam]);
		//数据集转数组
		$withdrawalsList = $resultData->toArray()['data'];
		//部分元素重新赋值
		$stateColor = config('manage.color');
		$pageTotal['countPrice'] = 0;
		$pageTotal['countFee'] = 0;
		foreach ($withdrawalsList as $key => &$value) {
			$value['stateColor'] = $stateColor[$value['state']];
			//分页统计
			$pageTotal['countPrice'] += $value['price'];
			$pageTotal['countFee'] += $value['fee'];
		}

		// 权限查询
		$powerWhere = 'uid = '.session('manage_userid').' AND (cid = 3 OR role_id = 245)';
		$power = model('ManageUserRole')->getUserPower($powerWhere);

		return array(
			'data'				=>	$withdrawalsList,
			'pageTotal'			=>	$pageTotal,
			'page'				=>	$resultData->render(),//分页
			'where'				=>	$pageParam,
			'withdrawalsState'	=>	config('custom.withdrawalsState'),
			'power'				=>	$power,
		);
	}

	/**
	 * 风控审核view
	 */
	public function controlAuditView(){
		$param = input('get.');

		// 支持通过id或order_number查询
		if (isset($param['id']) && $param['id']) {
			$data = $this->where('id', $param['id'])->find();
		} elseif (isset($param['order_number']) && $param['order_number']) {
			$data = $this->where('order_number', $param['order_number'])->find();
		} else {
			$data = null;
		}

		return array(
			'data'	=>	$data
		);
	}

	/**
	 * 风控审核
	 */
	public function controlAudit(){
		$param = input('post.');
		if(!$param) return '提交失败';

		$controlAuditTime = cache('CA_controlAuditTime'.session('manage_userid')) ? cache('CA_controlAuditTime'.session('manage_userid')) : time()-2;
		if(time() - $controlAuditTime < 2){
			return ' 2 秒内不能重复提交';
		}
		cache('CA_controlAuditTime'.session('manage_userid'), time(), 10);

		$orderNumber = $param['order_number'];
		unset($param['order_number']);

		//获取订单信息 - 支持重新审核已处理的订单
		$where[] = array('order_number','=',$orderNumber);
		$where[] = array('state','IN',[1,2,3]); // 允许审核中、已支付、拒绝支付的订单重新审核
		$orderInfo = $this->where($where)->find();
		if (!$orderInfo) return '订单不存在';
		$orderInfo = $orderInfo->toArray();
		// 过滤和验证更新字段
		$updateData = [];
		if(isset($param['examine'])) {
			$updateData['examine'] = intval($param['examine']);
		}
		if(isset($param['remarks'])) {
			$updateData['remarks'] = trim($param['remarks']);
		}
		// 添加管理员ID和处理时间
		$updateData['aid'] = session('manage_userid');
		$updateData['set_time'] = time();

		// 更新订单
		$res = $this->where('order_number',$orderNumber)->update($updateData);
		if(!$res) {
			// 记录详细错误信息用于调试
			\think\facade\Log::error('提现审核更新失败', [
				'order_number' => $orderNumber,
				'update_data' => $updateData,
				'original_param' => $param
			]);
			return '操作失败：数据更新失败';
		}

		switch ($param['examine']) {
			case 2:				
				//构造备注信息
				$totalRefund = $orderInfo['price'] + $orderInfo['fee'];
				$remarksTemp = '订单 '.$orderInfo['order_number'].' 取款失败，退回资金：'.$orderInfo['price'].'（提现金额）+ '.$orderInfo['fee'].'（手续费）= '.$totalRefund;
				$remarks = (isset($param['remarks']) && $param['remarks'] && $param['remarks'] !== $orderInfo['remarks']) ? $param['remarks'] : $remarksTemp;
				//更新订单
				$orderUpdateArray = array(
					'aid'		=>	session('manage_userid'),
					'state'		=>	2,
					'set_time'	=>	time(),
					'remarks'	=>	$remarks
				);
				$res2 = $this->where('id',$orderInfo['id'])->update($orderUpdateArray);
				if(!$res2) {
					$this->where('id',$orderInfo['id'])->update($orderInfo);
					return '操作失败2';
				}

				//获取用户余额
				$balance = model('UserTotal')->field('balance')->where('uid',$orderInfo['uid'])->find();
				//更新用户余额
				$res3 = model('UserTotal')->where('uid',$orderInfo['uid'])->inc('balance',$orderInfo['price']+$orderInfo['fee'])->update();
				if(!$res3) {
					$this->where('id',$orderInfo['id'])->update($orderInfo);
					return '操作失败3';
				}

				$res4 = model('TradeDetails')->where('order_number',$orderInfo['order_number'])->update(array('state'=>2,'remarks'=>'审核未通过，资金已退回'));
				if(!$res4) {
					$this->where('order_number',$orderNumber)->update($orderInfo);
					model('UserTotal')->where('uid',$orderInfo['uid'])->dec('balance',$orderInfo['price']+$orderInfo['fee'])->update();
					return '操作失败4';
				}
				$tradeDetailsArray = array(
					'uid'                    =>	$orderInfo['uid'],
					'order_number'           =>	$orderInfo['order_number'],
					'trade_type'             =>	TradeType::WITHDRAWAL_REFUND, // 提现退款（收入类型）
					'trade_before_balance'   =>	$balance['balance'],
					'trade_amount'           =>	$orderInfo['price'] + $orderInfo['fee'], // 提现金额+手续费
					'account_balance'        =>	$balance['balance'] + $orderInfo['price'] + $orderInfo['fee'],
					//'account_frozen_balance' => $balance['frozen_balance'] - $orderInfo['price'],
					'remarks'                =>	$remarks,
					//'types'                  =>	2,
					'isadmin'                =>	1,
					//'isdaily'                => 2
				);
				$res4 = model('common/TradeDetails')->tradeDetails($tradeDetailsArray);
				if(!$res4) {
					$this->where('id',$orderInfo['id'])->update($orderInfo);
					model('UserTotal')->where('uid',$orderInfo['uid'])->dec('balance',$orderInfo['price'])->update();
					return '操作失败4';
				}

				//添加操作日志
				model('Actionlog')->actionLog(session('manage_username'),'审核订单号为'.$orderInfo['order_number'].'的提现订单。处理状态：审核未通过',1);

				// 返回详细的处理结果
				return json([
					'code' => 1,
					'msg' => '审核拒绝成功',
					'data' => [
						'order_number' => $orderInfo['order_number'],
						'action' => 'reject',
						'amount' => $orderInfo['price'],
						'fee' => $orderInfo['fee'],
						'refund_amount' => $orderInfo['price'] + $orderInfo['fee'],
						'message' => '订单已拒绝，资金已退回用户账户'
					]
				]);
				break;
			
			case 1:
				// 审核通过，设置为待支付状态（不立即执行代付）
				$updateData = [
					'aid' => session('manage_userid'),
					'state' => 4, // 4=待支付状态
					'set_time' => time(),
					'remarks' => $param['remarks'] ?? '审核通过，等待支付'
				];

				$res2 = $this->where('id', $orderInfo['id'])->update($updateData);
				if(!$res2) {
					return '更新订单状态失败';
				}

				// 更新交易流水状态
				model('TradeDetails')->where('order_number', $orderInfo['order_number'])->update([
					'state' => PaymentStatus::WITHDRAWAL_AWAITING_PAYMENT,
					'remarks' => '审核通过，等待支付'
				]);

				//添加操作日志
				model('Actionlog')->actionLog(session('manage_username'),'审核订单号为'.$orderNumber.'的提现订单。处理状态：审核通过，等待支付',1);

				// 返回详细的处理结果
				return json([
					'code' => 1,
					'msg' => '审核通过成功',
					'data' => [
						'order_number' => $orderInfo['order_number'],
						'action' => 'approve',
						'amount' => $orderInfo['price'],
						'message' => '订单已审核通过，等待执行支付'
					]
				]);
				break;
		}

		return json(['code' => 1, 'msg' => '操作成功']);
	}

	/**
	 * 执行支付（选择渠道并执行代付）
	 */
	public function executePayment(){
		$param = input('post.');
		if(!$param) return '提交失败';

		$paymentTime = cache('EP_paymentTime'.session('manage_userid')) ? cache('EP_paymentTime'.session('manage_userid')) : time()-2;
		if(time() - $paymentTime < 2){
			return ' 2 秒内不能重复提交';
		}
		cache('EP_paymentTime'.session('manage_userid'), time(), 10);

		$orderNumber = $param['order_number'];
		$channelId = $param['channel_id'];

		if(empty($channelId)) {
			return '请选择代付渠道';
		}

		//获取订单信息 - 只允许待支付状态的订单
		$where[] = array('order_number','=',$orderNumber);
		$where[] = array('state','=',PaymentStatus::WITHDRAWAL_AWAITING_PAYMENT);
		$orderInfo = $this->where($where)->find();
		if (!$orderInfo) return '订单不存在或状态不正确';
		$orderInfo = $orderInfo->toArray();

		// 验证渠道是否存在且启用
		$channel = model('WithdrawalChannel')->where('id', $channelId)->where('state', 1)->find();
		if(!$channel) {
			return '选择的代付渠道不存在或已禁用';
		}

		// 执行代付
		$transactionService = new \app\common\service\TransactionService();
		$result = $transactionService->processWithdrawal($orderInfo, $channelId);

		if($result['code'] == 1) {
			// 代付请求成功，设置为已支付
			$updateData = [
				'aid' => session('manage_userid'),
				'channel_id' => $channelId,
				'state' => PaymentStatus::WITHDRAWAL_PAID,
				'set_time' => time(),
				'success_time' => time(),
				'remarks' => $param['remarks'] ?? '代付成功'
			];

			$res2 = $this->where('id', $orderInfo['id'])->update($updateData);
			if(!$res2) {
				return '更新订单状态失败';
			}

			// 更新交易流水状态
			model('TradeDetails')->where('order_number', $orderInfo['order_number'])->update([
				'state' => PaymentStatus::WITHDRAWAL_PAID,
				'remarks' => '代付成功'
			]);

			//添加操作日志
			model('Actionlog')->actionLog(session('manage_username'),'执行订单号为'.$orderNumber.'的代付。处理状态：代付成功',1);

			return 1; // 成功
		} else {
			// 代付失败，设置为拒绝支付并退还余额
			$remarks = '代付失败：' . $result['msg'] . '，资金已退回';

			//更新订单状态为拒绝支付
			$orderUpdateArray = array(
				'aid'		=>	session('manage_userid'),
				'state'		=>	PaymentStatus::WITHDRAWAL_REJECTED,
				'set_time'	=>	time(),
				'remarks'	=>	$remarks
			);
			$res2 = $this->where('id',$orderInfo['id'])->update($orderUpdateArray);
			if(!$res2) {
				return '更新订单状态失败';
			}

			//获取用户余额并退还
			$balance = model('UserTotal')->field('balance')->where('uid',$orderInfo['uid'])->find();
			model('UserTotal')->where('uid',$orderInfo['uid'])->inc('balance',$orderInfo['price']+$orderInfo['fee'])->update();

			// 更新交易流水
			model('TradeDetails')->where('order_number',$orderInfo['order_number'])->update(array('state'=>PaymentStatus::WITHDRAWAL_REJECTED,'remarks'=>$remarks));

			//添加操作日志
			model('Actionlog')->actionLog(session('manage_username'),'执行订单号为'.$orderNumber.'的代付。处理状态：代付失败，资金已退回',1);

			return $result['msg']; // 返回代付失败原因
		}
	}



	/**
	 * 出款
	 */
	public function withdrawalsPayment(){
		if(!request()->isAjax()) return '非法提交';

		$param = input('post.');
		if(!$param) return '提交失败';

		// 获取出款状态
		// $cashStatus = model('Setting')->where('id','>',0)->value('cash_status');
		// if($cashStatus != 1) return '未开启出款功能';

		// 时间段
		$startTime = mktime(8,0,0,date('m'),date('d'),date('Y'));
		$endTime   = mktime(18,0,0,date('m'),date('d'),date('Y'));
        //if(time()<$startTime || time()>$endTime) return '当前不在处理时间段';

        // 获取出款单信息
		$drawInfo = $this->where('id', $param['id'])->find();
		if(!$drawInfo) return '订单不存在';
		
		
		
		// 优先从配置文件获取银行信息
		$bankInfo = $this->getBankInfoFromConfig($drawInfo['bank_id']);
		if (!$bankInfo) {
			// 如果配置文件中没有，再从数据库查找
			$bankItem = model('Bank')->where('id', $drawInfo['bank_id'])->find();
			if(!$bankItem) return "未知的取款类型";
		} else {
			// 使用配置文件中的银行信息创建虚拟bankItem
			$bankItem = [
				'id' => $drawInfo['bank_id'],
				'bank_name' => $bankInfo['bank_name'],
				'q_start_time' => '00:00:00',
				'q_end_time' => '23:59:59'
			];
		}
		$startTime = strtotime(date('Y-m-d').' '.$bankItem['q_start_time']);
		$endTime = strtotime(date('Y-m-d').' '.$bankItem['q_end_time']);
		if(time()<$startTime || time()>$endTime){
		    return '当前不在处理时间段';
		}

        // 优先使用新的代付渠道系统
        $channelId = $drawInfo['channel_id'];

        // 如果提现记录中没有渠道ID，选择默认渠道
        if (!$channelId) {
            // 根据bank_id选择合适的默认渠道
            if ($drawInfo['bank_id'] == 0 || $drawInfo['bank_id'] == 999) {
                // USDT提现，只能选择传统代付（人工处理）
                $defaultChannel = model('WithdrawalChannel')->where(['state' => 1, 'mode' => 'traditional'])->find();
            } else {
                // 银行卡提现，优先选择WatchPay或JayaPay
                $defaultChannel = model('WithdrawalChannel')->where('state', 1)
                    ->whereIn('mode', ['watchPay', 'jaya_pay'])
                    ->order('sort', 'asc')
                    ->find();
                if (!$defaultChannel) {
                    // 如果没有第三方渠道，使用传统代付
                    $defaultChannel = model('WithdrawalChannel')->where(['state' => 1, 'mode' => 'traditional'])->find();
                }
            }
            
            if ($defaultChannel) {
                $channelId = $defaultChannel['id'];
                // 更新提现记录的渠道ID
                $this->where('id', $drawInfo['id'])->update(['channel_id' => $channelId]);
            }
        }

        if ($channelId) {
            // 获取渠道信息
            $channel = model('WithdrawalChannel')->where('id', $channelId)->find();
            if (!$channel) {
                return '代付渠道不存在';
            }

            // USDT类型只能人工处理
            if ($drawInfo['bank_id'] == 0 || $drawInfo['bank_id'] == 999) {
                if ($channel['mode'] != 'traditional') {
                    return 'USDT提现只支持人工转账，不支持第三方代付';
                }
                // USDT人工处理，直接标记为出款成功（需要人工确认转账）
                $this->paymentSuccess(['order_number'=>$drawInfo['order_number']]);
                return 1;
            }

            // 银行卡类型，根据选择的渠道处理
            switch ($channel['mode']) {
                case 'watchPay':
                case 'jaya_pay':
                    // 第三方代付渠道，调用新的代付服务
                    $transactionService = new \app\common\service\TransactionService();
                    $result = $transactionService->processWithdrawal($drawInfo->toArray(), $channelId);

                    if ($result['code'] == 1) {
                        // 更新提现记录状态
                        $this->where('id', $drawInfo['id'])->update([
                            'state' => 5, // 代付中
                            'process_time' => time()
                        ]);
                        return 1; // 代付请求已提交
                    } else {
                        // 代付失败，更新订单备注记录失败原因
                        $failureRemark = "[" . date('Y-m-d H:i:s') . "] {$channel['name']}代付失败: " . $result['msg'];
                        $this->where('id', $drawInfo['id'])->update([
                            'remarks' => $failureRemark,
                            'process_time' => time()
                        ]);

                        // 记录失败日志
                        \think\facade\Log::error("单个代付失败: 订单ID={$drawInfo['id']}, 订单号={$drawInfo['order_number']}, 错误原因={$result['msg']}");

                        return $result['msg']; // 返回错误信息
                    }
                    break;

                case 'traditional':
                default:
                    // 银行卡手动付款，继续执行旧代付系统逻辑
                    break; // 只跳出switch
            }
            // 如果是传统代付的银行卡，继续执行下面的旧代付系统逻辑
        }

        // 兼容旧的代付系统（只在传统代付且没有新渠道配置时使用）
        $drawConfig = model('DrawConfig')->where('state',1)->find();
        if(!$drawConfig) {
            // 如果没有旧系统配置，但有新渠道配置，直接返回成功（由新系统处理）
            if ($channelId) {
                return '代付渠道配置错误，请联系管理员';
            }
            return '系统暂未配置代付渠道，请联系管理员';
        }

		// 获取所有充值渠道
		$rechargeArr = model('RechangeType')->field('id,submitUrl')->order('id','asc')->select()->toArray();

		if(!$rechargeArr) return '取款类型不可用 - 1';
		
		// 简化渠道匹配逻辑 - 使用第一个可用的充值渠道
		$rechargeId = $rechargeArr[0]['id'];
		// 获取银行名称 - 优先从配置获取
		$bankInfo = $this->getBankInfoFromConfig($drawInfo['bank_id']);
		if ($bankInfo) {
			$bankName = $bankInfo['bank_name'];
		} else {
			$bankName = model('Bank')->where('id', $drawInfo['bank_id'])->value('bank_name');
		}
		if(!$bankName) return '取款类型不可用 - 3';
		// 获取渠道对应的银行代码
		if ($drawInfo['bank_id'] == 0 || $drawInfo['bank_id'] == 999) {
			// USDT 类型
			$bankCode = 'USDT';
		} else {
			// 从数据库获取具体的银行代码，或从配置文件获取
			$bankCode = model('Bank')->where(['bank_name'=>$bankName,'pay_type'=>$rechargeId])->value('bank_code');
			if (!$bankCode) {
				// 如果数据库中没有，尝试从配置文件获取对应的编码
				$configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
				if (file_exists($configPath)) {
					$paymentConfig = include($configPath);
					$bankMapping = $paymentConfig['bank_code_mapping'] ?? [];
					foreach ($bankMapping as $name => $codes) {
						if ($codes['bank_id'] == $drawInfo['bank_id']) {
							$bankCode = $codes['watchpay'] ?? $codes['jayapay'] ?? 'BANK_CARD';
							break;
						}
					}
				}
			}
		}
		if(!$bankCode) return '取款类型不可用 - 4';

		// 构造提交数据
		switch ($drawConfig['id']) {
			case '999':
				$dataArray = [
					'uid'				=>	$drawInfo['uid'],
					'order'				=>	$drawInfo['order_number'],
					'amount'			=>	$drawInfo['price'],
					'account_Name'		=>	$drawInfo['card_name'],
					'account_Number'	=>	$drawInfo['card_number'],
					'bank_Code'			=>	$bankName,
				];
				break;
			
			default:
				$dataArray = [
					'uid'				=>	$drawInfo['uid'],
					'order'				=>	$drawInfo['order_number'],
					'amount'			=>	$drawInfo['price'],
					'account_Name'		=>	$drawInfo['card_name'],
					'account_Number'	=>	$drawInfo['card_number'],
					'bank_Code'			=>	$bankCode,
				];
				break;
		}

		// 出款
		$ch = curl_init();	
		curl_setopt($ch, CURLOPT_URL, $drawConfig['submit_url']);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_HEADER, false);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($dataArray));  
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		$result = curl_exec($ch);
		curl_close($ch);

		if (!$result) return '已提交';

	    $resultArray = json_decode($result, true);
	    switch ($drawConfig['id']) {
	    	case 1:
	    	case 2:
	    		if(!$resultArray['error_Msg'] && $resultArray['bank_Status']=='I'){
	        		$this->paymentSuccess(['order_number'=>$drawInfo['order_number']]);

					$ajaxStr = 1;
				}else{
					$ajaxStr = $resultArray['error_Msg'].' - '.$resultArray['bank_Status'];
				}
	    		break;

	    	default:
				if ($result == 1 || $result == 'Y') {
	        		$this->paymentSuccess(['order_number'=>$drawInfo['order_number']]);

	        		$ajaxStr = 1;
	        	} else {
	        		$ajaxStr = $result;
	        	}
	    		break;
	    }

	    return $ajaxStr;
	}

	/**
	 * 出款成功
	 */
	public function paymentSuccess($param=[]){
		//更新提现订单
		$this->where('order_number',$param['order_number'])->update(array('state'=>6,'aid'=>session('manage_userid')));
		//获取提现订单信息
		$orderInfo = $this->field('uid,price')->where('order_number',$param['order_number'])->find();

		//更新每日报表
		$reportFormArray = array(
			'uid'		=>	$orderInfo['uid'],
			'type'		=>	2,
			'price'		=>	$orderInfo['price'],
			'isadmin'	=>	1
		);
		model('UserDaily')->updateReportForm($reportFormArray);

		//更新流水
		model('TradeDetails')->where('order_number',$param['order_number'])->update(array('state'=>1));
	}

	/**
	 * 根据bank_id从配置文件获取银行信息
	 */
	private function getBankInfoFromConfig($bank_id)
	{
		try {
			// 标准USDT配置
			if ($bank_id == 999) {
				return [
					'bank_id' => 999,
					'bank_name' => 'USDT',
					'display_name' => 'USDT',
					'type' => 'usdt'
				];
			}

			// 处理历史数据的兼容性映射
			if ($bank_id == 0) {
				// 旧的USDT数据，映射到标准999
				return [
					'bank_id' => 999,
					'bank_name' => 'USDT',
					'display_name' => 'USDT',
					'type' => 'usdt'
				];
			}

			// 从配置文件获取银行信息
			$configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
			if (file_exists($configPath)) {
				$paymentConfig = include($configPath);
				$bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

				foreach ($bankMapping as $bankName => $codes) {
					if (isset($codes['bank_id']) && $codes['bank_id'] == $bank_id) {
						return [
							'bank_id' => $bank_id,
							'bank_name' => $bankName,
							'display_name' => $bankName,
							'type' => 'bank'
						];
					}
				}
			}

			// 如果没有找到，返回null
			return null;

		} catch (\Exception $e) {
			// 出错时返回null
			return null;
		}
	}
}
