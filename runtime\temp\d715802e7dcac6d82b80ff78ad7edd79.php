<?php /*a:1:{s:56:"/var/www/html/application/manage/view/bet/financial.html";i:1753373240;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>资金流水</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form search">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索类型</label>
                                <div class="layui-input-inline">
                                    <select name="search_type" lay-search="">
                                        <option value="username">用户名</option>
                                        <option value="order_number">订单号</option>
                                        <option value="trade_number">流水号</option>
                                        <option value="remarks">说明</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索内容</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" name="search_content" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">交易类型</label>
                                <div class="layui-input-inline">
                                    <select name="trade_type" lay-search="">
                                        <?php foreach(app('config')->get('custom.transactionType') as $key=>$value): ?>
                                        <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($value); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">交易余额</label>
                                <div class="layui-input-inline" style="width: 150px;">
                                    <input type="number" name="price1" placeholder="最小金额" class="layui-input" step="0.01" min="0" max="99999999999999.99" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width: 150px;">
                                    <input type="number" name="price2" placeholder="最大金额" class="layui-input" step="0.01" min="0" max="99999999999999.99" data-format="currency" data-validate="large-number">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="datetime_range" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-block" style="text-align: center;">
                                <button type="button" class="layui-btn" data-type="search">搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="financial" lay-filter="financial"></table>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="action">
        <div class="layui-btn-group">
            <button type="button" class="layui-btn layui-btn-danger layui-btn-xs" lay-event="details">详情</button>
        </div>
    </script>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bet.js"></script>
<script>
    layui.use(['table'], function(){
        var $ = layui.$
        ,table = layui.table;

        //方法级渲染
        table.render({
            elem: '#financial'
            ,title: '资金流水'
            ,url: '/manage/bet/financial'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true, totalRowText: '合计'}
                ,{field: 'order_number', title: '订单号', sort: true}
                ,{field: 'trade_number', title: '流水号', sort: true}
                ,{field: 'username', title: '所属账户', sort: true}
                ,{field: 'source_username', title: '来源用户', sort: true, templet: function(d){
                    return d.source_username ? d.source_username : '-';
                }}
                ,{field: 'tradeType', title: '交易类型', sort: true}
                ,{field: 'trade_amount', title: '交易金额', sort: true, totalRow: true}
                ,{field: 'trade_before_balance', title: '交易前余额', sort: true}
                ,{field: 'account_balance', title: '交易后余额', sort: true}
               // ,{field: 'account_total_balance', title: '交易后总资产', sort: true}
                ,{field: 'statusStr', title: '交易状态', sort: true}
                ,{field: 'payway_str', title: '支付方式', sort: true}
                ,{field: 'remarks', title: '说明备注', sort: true}
                ,{field: 'trade_time', title: '交易时间', sort: true}
                ,{title: '操作', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            // ,toolbar: '#toolbarDemo'
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
        });

        //监听排序事件
        table.on('sort(financial)', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            //尽管我们的 table 自带排序功能，但并没有请求服务端。
            //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
            table.reload('financial', {
                initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    sortField: obj.field //排序字段
                    ,sortType: obj.type //排序方式
                }
            });
        });

        active = {
            search: function(){
                //执行重载
                table.reload('financial', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    ,where: {
                        search_type: $("select[name='search_type'] option:selected").val()
                        ,search_content: $("input[name='search_content']").val()
                        ,trade_type: $("select[name='trade_type'] option:selected").val()
                        ,price1: $("input[name='price1']").val()
                        ,price2: $("input[name='price2']").val()
                        ,datetime_range: $("input[name='datetime_range']").val()

                    }
                }, 'data');
            }
        };

        $('.search .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
</body>
</html>