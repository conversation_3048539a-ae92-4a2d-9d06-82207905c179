<?php
/**
 * 加密系统测试脚本
 * 用于验证RSA+AES混合加密功能
 */

// 引入ThinkPHP框架
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
}

// 模拟ThinkPHP环境
if (!class_exists('think\facade\Log')) {
    class Log {
        public static function error($msg, $data = []) {
            echo "[ERROR] $msg\n";
        }
        public static function info($msg, $data = []) {
            echo "[INFO] $msg\n";
        }
    }
}

if (!class_exists('think\facade\Cache')) {
    class Cache {
        private static $cache = [];
        public static function get($key) {
            return isset(self::$cache[$key]) ? self::$cache[$key] : null;
        }
        public static function set($key, $value, $expire = 0) {
            self::$cache[$key] = $value;
            return true;
        }
    }
}

// 创建命名空间别名
if (!class_exists('think\facade\Log')) {
    class_alias('Log', 'think\facade\Log');
}
if (!class_exists('think\facade\Cache')) {
    class_alias('Cache', 'think\facade\Cache');
}

require_once __DIR__ . '/../application/common/service/CryptoService.php';

use app\common\service\CryptoService;

class CryptoTest
{
    /**
     * 测试RSA密钥生成
     */
    public function testRsaKeyGeneration()
    {
        echo "=== 测试RSA密钥生成 ===\n";
        
        try {
            $keys = CryptoService::initRsaKeys();
            
            echo "私钥长度: " . strlen($keys['private_key']) . "\n";
            echo "公钥长度: " . strlen($keys['public_key']) . "\n";
            echo "私钥格式检查: " . (strpos($keys['private_key'], '-----BEGIN PRIVATE KEY-----') !== false ? '✓' : '✗') . "\n";
            echo "公钥格式检查: " . (strpos($keys['public_key'], '-----BEGIN PUBLIC KEY-----') !== false ? '✓' : '✗') . "\n";
            echo "RSA密钥生成测试: ✓ 通过\n\n";
            
            return $keys;
            
        } catch (Exception $e) {
            echo "RSA密钥生成测试: ✗ 失败 - " . $e->getMessage() . "\n\n";
            return false;
        }
    }
    
    /**
     * 测试AES加密解密
     */
    public function testAesEncryption()
    {
        echo "=== 测试AES加密解密 ===\n";
        
        try {
            $testData = "这是一个测试数据：Hello World! 123456";
            $aesKey = CryptoService::generateAesKey();
            
            echo "原始数据: {$testData}\n";
            echo "AES密钥: {$aesKey}\n";
            
            // AES加密
            $encryptResult = CryptoService::aesEncrypt($testData, $aesKey);
            if (!$encryptResult) {
                throw new Exception('AES加密失败');
            }
            
            echo "加密数据: " . substr($encryptResult['data'], 0, 50) . "...\n";
            echo "IV: " . $encryptResult['iv'] . "\n";
            
            // AES解密
            $decryptedData = CryptoService::aesDecrypt(
                $encryptResult['data'], 
                $aesKey, 
                $encryptResult['iv']
            );
            
            if ($decryptedData === false) {
                throw new Exception('AES解密失败');
            }
            
            echo "解密数据: {$decryptedData}\n";
            echo "数据一致性: " . ($testData === $decryptedData ? '✓' : '✗') . "\n";
            echo "AES加密解密测试: ✓ 通过\n\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "AES加密解密测试: ✗ 失败 - " . $e->getMessage() . "\n\n";
            return false;
        }
    }
    
    /**
     * 测试RSA加密解密AES密钥
     */
    public function testRsaAesKeyEncryption()
    {
        echo "=== 测试RSA加密解密AES密钥 ===\n";
        
        try {
            // 生成AES密钥
            $aesKey = CryptoService::generateAesKey();
            echo "原始AES密钥: {$aesKey}\n";
            
            // RSA加密AES密钥
            $encryptedKey = CryptoService::rsaEncryptAesKey($aesKey);
            if (!$encryptedKey) {
                throw new Exception('RSA加密AES密钥失败');
            }
            
            echo "加密后密钥: " . substr($encryptedKey, 0, 50) . "...\n";
            
            // RSA解密AES密钥
            $decryptedKey = CryptoService::rsaDecryptAesKey($encryptedKey);
            if (!$decryptedKey) {
                throw new Exception('RSA解密AES密钥失败');
            }
            
            echo "解密后密钥: {$decryptedKey}\n";
            echo "密钥一致性: " . ($aesKey === $decryptedKey ? '✓' : '✗') . "\n";
            echo "RSA加密解密AES密钥测试: ✓ 通过\n\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "RSA加密解密AES密钥测试: ✗ 失败 - " . $e->getMessage() . "\n\n";
            return false;
        }
    }
    
    /**
     * 测试盐值生成和验证
     */
    public function testSaltGeneration()
    {
        echo "=== 测试盐值生成和验证 ===\n";
        
        try {
            // 生成盐值
            $salt1 = CryptoService::generateSalt();
            $salt2 = CryptoService::generateSalt();
            
            echo "盐值1: {$salt1}\n";
            echo "盐值2: {$salt2}\n";
            echo "盐值唯一性: " . ($salt1 !== $salt2 ? '✓' : '✗') . "\n";
            
            // 测试盐值签名
            $signature = CryptoService::generateDataSignature($salt1);
            echo "盐值签名: {$signature}\n";
            
            $verifyResult = CryptoService::verifyDataSignature($salt1, $signature);
            echo "签名验证: " . ($verifyResult ? '✓' : '✗') . "\n";
            
            // 测试盐值唯一性检查
            $uniqueCheck1 = CryptoService::checkSaltUnique($salt1);
            $uniqueCheck2 = CryptoService::checkSaltUnique($salt1); // 重复检查
            
            echo "首次唯一性检查: " . ($uniqueCheck1 ? '✓' : '✗') . "\n";
            echo "重复唯一性检查: " . (!$uniqueCheck2 ? '✓' : '✗') . "\n";
            echo "盐值生成和验证测试: ✓ 通过\n\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "盐值生成和验证测试: ✗ 失败 - " . $e->getMessage() . "\n\n";
            return false;
        }
    }
    
    /**
     * 测试完整的加密解密流程
     */
    public function testCompleteFlow()
    {
        echo "=== 测试完整加密解密流程 ===\n";
        
        try {
            // 模拟前端发送的数据
            $originalData = [
                'username' => 'testuser',
                'password' => 'testpass123',
                'timestamp' => time()
            ];
            
            echo "原始数据: " . json_encode($originalData, JSON_UNESCAPED_UNICODE) . "\n";
            
            // 1. 生成盐值
            $salt = CryptoService::generateSalt();
            
            // 2. 构造要加密的数据
            $payload = [
                'salt' => $salt,
                'payload' => $originalData
            ];
            
            // 3. 加密响应（模拟完整流程）
            $encryptResult = CryptoService::encryptResponse($originalData);
            if (!$encryptResult) {
                throw new Exception('加密响应失败');
            }
            
            echo "加密结果包含字段: " . implode(', ', array_keys($encryptResult)) . "\n";
            
            // 4. 解密请求（模拟完整流程）
            $decryptResult = CryptoService::decryptRequest($encryptResult);
            if (!$decryptResult['success']) {
                throw new Exception('解密请求失败: ' . $decryptResult['error']);
            }
            
            echo "解密数据: " . json_encode($decryptResult['data'], JSON_UNESCAPED_UNICODE) . "\n";
            echo "数据一致性: " . (json_encode($originalData) === json_encode($decryptResult['data']) ? '✓' : '✗') . "\n";
            echo "完整加密解密流程测试: ✓ 通过\n\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "完整加密解密流程测试: ✗ 失败 - " . $e->getMessage() . "\n\n";
            return false;
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始运行加密系统测试...\n\n";
        
        $tests = [
            'testRsaKeyGeneration',
            'testAesEncryption', 
            'testRsaAesKeyEncryption',
            'testSaltGeneration',
            'testCompleteFlow'
        ];
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test) {
            if ($this->$test()) {
                $passed++;
            }
        }
        
        echo "=== 测试结果汇总 ===\n";
        echo "通过: {$passed}/{$total}\n";
        echo "状态: " . ($passed === $total ? '✓ 全部通过' : '✗ 部分失败') . "\n";
        
        return $passed === $total;
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new CryptoTest();
    $result = $test->runAllTests();
    exit($result ? 0 : 1);
} else {
    echo "请在命令行环境下运行此测试脚本\n";
    echo "使用方法: php test/crypto_test.php\n";
}
