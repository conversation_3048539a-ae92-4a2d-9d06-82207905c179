<?php /*a:1:{s:56:"/var/www/html/application/manage/view/bet/task_edit.html";i:1754248761;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加任务</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务分类</label>
                                <div class="layui-input-inline">
                                    <select name="task_class" lay-verify="required" lay-search="">
                                        <?php foreach($taskClass as $key=>$value): ?>
                                        <option value="<?php echo htmlentities($value['id']); ?>"<?php if($data['task_class'] == $value['id']): ?> selected<?php endif; ?>><?php echo htmlentities($value['lang']); ?>-<?php echo htmlentities($value['group_name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">语言</label>
                                <div class="layui-input-inline">
                                     <select name="lang" lay-verify="required" lay-search="">
                                     <?php foreach(config('custom.lang') as $key=>$value): ?>
                                        <option value="<?php echo htmlentities($key); ?>" <?php if($data['lang'] == $key): ?> selected<?php endif; ?>><?php echo htmlentities($value); ?></option>
									 <?php endforeach; ?>

                                    </select>
                                </div>
                            </div>
							<?php if($tplid): ?>
							<div class="layui-form-item">
                                <label class="layui-form-label">任务重复数量</label>
                                <div class="layui-input-block">
                                    <input type="text" name="repeat_num" autocomplete="off" value="<?php echo isset($data['repeat_num']) ? htmlentities($data['repeat_num']) : ''; ?>" placeholder="请输入任务重复数量" class="layui-input">
                                </div>
                            </div>
							<?php endif; ?>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务标题</label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" value="<?php echo isset($data['title']) ? htmlentities($data['title']) : ''; ?>" autocomplete="off" placeholder="请输入任务标题" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务简介</label>
                                <div class="layui-input-block">
                                    <textarea name="content" placeholder="请输入任务简介" class="layui-textarea"><?php echo isset($data['content']) ? htmlentities($data['content']) : ''; ?></textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务主图</label>
                                <div class="layui-input-block">
                                    <input type="text" name="main_image" id="main-image-url" value="<?php echo isset($data['main_image']) ? htmlentities($data['main_image']) : ''; ?>" autocomplete="off" placeholder="请输入主图URL链接" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">
                                        <img id="main-image-preview" src="<?php echo isset($data['main_image']) ? htmlentities($data['main_image']) : ''; ?>" style="max-width: 200px; max-height: 200px; margin-top: 10px; <?php if(empty($data['main_image'])): ?>display: none;<?php endif; ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务详情图</label>
                                <div class="layui-input-block">
                                    <div id="detail-images-container">
                                        <?php 
                                        $detail_images = [];
                                        if (!empty($data['detail_image'])) {
                                            if (is_array($data['detail_image'])) {
                                                // 已经是数组（从taskEdit方法传来）
                                                $detail_images = $data['detail_image'];
                                            } elseif (is_string($data['detail_image'])) {
                                                // 是字符串，尝试解码
                                                $decoded = json_decode($data['detail_image'], true);
                                                if (is_array($decoded)) {
                                                    $detail_images = $decoded;
                                                } else {
                                                    // 兼容旧数据（单个URL字符串）
                                                    $detail_images = [$data['detail_image']];
                                                }
                                            }
                                        }
                                        if (empty($detail_images)) {
                                            $detail_images = [''];
                                        }
                                         foreach($detail_images as $key => $image_url): ?>
                                        <div class="detail-image-item" style="margin-bottom: 10px;">
                                            <div style="display: flex; align-items: center;">
                                                <input type="text" name="detail_images[]" class="layui-input detail-image-url" value="<?php echo htmlentities($image_url); ?>" autocomplete="off" placeholder="请输入详情图URL链接" style="flex: 1; margin-right: 10px;">
                                                <button type="button" class="layui-btn layui-btn-sm layui-btn-danger remove-detail-image" <?php if($key == 0 && count($detail_images) == 1): ?>style="display: none;"<?php endif; ?>>删除</button>
                                            </div>
                                            <div class="detail-image-preview-container" style="margin-top: 5px;">
                                                <img class="detail-image-preview" src="<?php echo htmlentities($image_url); ?>" style="max-width: 200px; max-height: 200px; <?php if(empty($image_url)): ?>display: none;<?php endif; ?>">
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="add-detail-image">
                                        <i class="layui-icon layui-icon-addition"></i> 添加详情图
                                    </button>
                                    <div class="layui-form-mid layui-word-aux">支持添加多个详情图</div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">购买价格</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="purchase_price" value="<?php echo isset($data['purchase_price']) ? htmlentities($data['purchase_price']) : 0; ?>" autocomplete="off" placeholder="请输入购买价格" class="layui-input" step="0.01" min="0" max="99999999999999.99" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid layui-word-aux">用户购买任务需要支付的金额</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务佣金</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="task_commission" value="<?php echo isset($data['task_commission']) ? htmlentities($data['task_commission']) : 0; ?>" autocomplete="off" placeholder="请输入任务佣金" class="layui-input" step="0.01" min="0" max="99999999999999.99" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid layui-word-aux">用户完成任务获得的佣金</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">购买数量</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="total_number" value="<?php echo isset($data['total_number']) ? htmlentities($data['total_number']) : 0; ?>" autocomplete="off" placeholder="请输入购买数量" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">购买次数</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="person_time" value="<?php echo isset($data['person_time']) ? htmlentities($data['person_time']) : 0; ?>" autocomplete="off" placeholder="请输入购买次数" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">次/人</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务总价</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="total_price" value="<?php echo isset($data['total_price']) ? htmlentities($data['total_price']) : 0; ?>" autocomplete="off" placeholder="请输入任务总价" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务类型</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="task_type" value="1" title="供应信息"<?php if($data['task_type'] == 1): ?> checked<?php endif; ?>>
                                    <input type="radio" name="task_type" value="2" title="需求信息"<?php if($data['task_type'] == 2): ?> checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">链接信息</label>
                                <div class="layui-input-block">
                                    <input type="text" name="link_info" value="<?php echo isset($data['link_info']) ? htmlentities($data['link_info']) : ''; ?>" autocomplete="off" placeholder="请输入链接信息" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务级别</label>
                                <div class="layui-input-block">
                                    <?php foreach($userLevel as $key=>$value): ?>
                                    <input type="radio" name="task_level" value="<?php echo htmlentities($value['grade']); ?>" title="<?php echo htmlentities($value['name']); ?>"<?php if($data['task_level'] == $value['grade']): ?> checked<?php endif; ?>>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">截止日期</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="end_time" value="<?php echo htmlentities($data['end_time']); ?>" autocomplete="off" placeholder="请选择截止日期" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">完成条件</label>
                                <div class="layui-input-block">
                                <?php if($data['finish_condition'] != ''): ?>
                                <input type="checkbox" name="finish_condition[0]" title="手机认证"<?php if(in_array(0, $data['finish_condition'])): ?> checked<?php endif; ?>>
                                <input type="checkbox" name="finish_condition[1]" title="微信认证"<?php if(in_array(1, $data['finish_condition'])): ?> checked<?php endif; ?>>
                                <input type="checkbox" name="finish_condition[2]" title="实名认证"<?php if(in_array(2, $data['finish_condition'])): ?> checked<?php endif; ?>>
                                <input type="checkbox" name="finish_condition[3]" title="身份认证"<?php if(in_array(3, $data['finish_condition'])): ?> checked<?php endif; ?>>
                                <?php endif; if($data['finish_condition'] == ''): ?>
                                <input type="checkbox" name="finish_condition[0]" title="手机认证"/>
                                <input type="checkbox" name="finish_condition[1]" title="微信认证"/>
                                <input type="checkbox" name="finish_condition[2]" title="实名认证"/>
                                <input type="checkbox" name="finish_condition[3]" title="身份认证"/>
                                <?php endif; ?>
                              </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">上传要求</label>
                                <div class="layui-input-block">
                                    <textarea name="requirement" placeholder="请输入上传要求" class="layui-textarea"><?php echo htmlentities($data['requirement']); ?></textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">一级分佣比例</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="task_rebate1" value="<?php echo isset($data['task_rebate1']) ? htmlentities($data['task_rebate1']) : '10.00'; ?>" autocomplete="off" placeholder="请输入一级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">二级分佣比例</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="task_rebate2" value="<?php echo isset($data['task_rebate2']) ? htmlentities($data['task_rebate2']) : '5.00'; ?>" autocomplete="off" placeholder="请输入二级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">三级分佣比例</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="task_rebate3" value="<?php echo isset($data['task_rebate3']) ? htmlentities($data['task_rebate3']) : '2.00'; ?>" autocomplete="off" placeholder="请输入三级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">显示状态</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="is_visible" value="1" title="显示"<?php if($data['is_visible'] == 1): ?> checked<?php endif; ?>>
                                    <input type="radio" name="is_visible" value="0" title="隐藏"<?php if($data['is_visible'] == 0): ?> checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">审核样例</label>
                                <div class="layui-upload layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal examine_demo">选择多文件</button>
                                    <div class="layui-form-mid layui-word-aux" style="float: right;">此项如不修改请不要上传文件，否则将覆盖原数据</div>
                                    <div class="layui-upload-list">
                                        <table class="layui-table">
                                        <thead>
                                            <tr>
                                                <th>文件名</th>
                                                <th>大小</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="demoList"></tbody>
                                        </table>
                                    </div>
                                    <button type="button" class="layui-btn" id="examine-demo-btn">开始上传</button>
                                </div>
                            </div>
                            <div class="layui-form-item step-holder">
                                <label class="layui-form-label">任务步骤</label>
                                <?php foreach($data['task_step'] as $key=>$value): ?>
                                <div class="layui-input-block" style="border: 1px solid #d2d2d2;border-radius: 3px;padding: 12px;display: flex;margin-bottom: 10px;align-items: flex-start;">
                                    <h4 style="background-color: #FFB800;width: 40px;height: 40px;border-radius: 20px;line-height: 40px;font-size: 16px;margin-right: 10px;text-align: center;margin-top: 50px;"><?php echo htmlentities($key); ?></h4>
                                    <div class="layui-upload-drag task-step" id="task-step-1" style="width: 10%;margin-right: 10px;">
                                        <i class="layui-icon layui-icon-upload"></i>
                                        <p>点击上传，或将文件拖拽到此处</p>
                                        <div <?php if(!$value['img']): ?>class="layui-hide"<?php endif; ?>>
                                            <hr>
                                            <img src="<?php echo isset($value['img']) ? htmlentities($value['img']) : ''; ?>" alt="上传成功后渲染" style="max-width: 150px">
                                            <p></p>
                                        </div>
                                    </div>
                                    <input type="hidden" name="task_step[<?php echo htmlentities($key); ?>][img]" value="<?php echo isset($value['img']) ? htmlentities($value['img']) : ''; ?>" class="layui-input">
                                    <textarea name="task_step[<?php echo htmlentities($key); ?>][describe]" placeholder="请输入步骤描述" class="layui-textarea" style="width: 70%;display: inline-block;height: 250px;"><?php echo isset($value['describe']) ? htmlentities($value['describe']) : ''; ?></textarea>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label"></label>
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-warm step-holder-add">
                                        <i class="layui-icon layui-icon-addition" style="font-size: 20px;"></i>
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-warm step-holder-del">
                                        <i class="layui-icon layui-icon-subtraction" style="font-size: 20px;"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
								<?php if(!$tplid): ?>
                                <input type="hidden" name="id" value="<?php echo isset($data['id']) ? htmlentities($data['id']) : 0; ?>" class="layui-input">
								<?php endif; ?>
                                <button class="layui-btn" lay-submit lay-filter="project_sub" data-type="<?php echo !empty($tplid) ? 'taskAdd' : 'taskEdit'; ?>">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bet.js"></script>

</body>
</html>