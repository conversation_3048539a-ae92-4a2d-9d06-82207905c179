<?php /*a:1:{s:79:"/www/wwwroot/www.lotteup.com/application/manage/view/bank/recharge_channel.html";i:**********;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>充值渠道</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form" action="/manage/bank/recharge_channel" method="get">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">渠道名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="name" placeholder="渠道名称" class="layui-input" value="<?php echo isset($where['name']) ? htmlentities($where['name']) : ''; ?>">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-inline">
                                    <select name="state" lay-verify="required" lay-search="">
                                        <option value="">全部</option>
                                        <option value="1"<?php if(isset($where['state']) and $where['state'] == 1): ?> selected<?php endif; ?>>开启</option>
                                        <option value="2"<?php if(isset($where['state']) and $where['state'] == 2): ?> selected<?php endif; ?>>关闭</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">充值金额</label>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="text" name="minPrice" value="<?php echo isset($where['minPrice']) ? htmlentities($where['minPrice']) : ''; ?>" placeholder="￥" class="layui-input">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="text" name="maxPrice" value="<?php echo isset($where['maxPrice']) ? htmlentities($where['maxPrice']) : ''; ?>" placeholder="￥" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">类型</label>
                                <div class="layui-input-inline">
                                    <select name="mode" lay-verify="required" lay-search="">
                                        <option value="">全部</option>
                                        <?php foreach(app('config')->get('custom.rechargeType') as $key=>$value): ?>
                                        <option value="<?php echo htmlentities($key); ?>"<?php if(isset($where['mode']) and $where['mode'] == $key): ?> selected<?php endif; ?>><?php echo htmlentities($value); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">客户端</label>
                                <div class="layui-input-inline">
                                    <select name="type" lay-verify="required" lay-search="">
                                        <option value="">全部</option>
                                        <option value="app"<?php if(isset($where['type']) and $where['type'] == 'app'): ?> selected<?php endif; ?>>APP</option>
                                        <option value="pc"<?php if(isset($where['type']) and $where['type'] == 'pc'): ?> selected<?php endif; ?>>PC</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <p style="text-align: center;"><button type="submit" class="layui-btn">搜索</button></p>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-btn-group" style="padding: 10px;">
                        <button type="button" class="layui-btn layui-btn-sm rechargeadd_btn">
                            <i class="layui-icon">&#xe654;</i>
                        </button>
                    </div>
                    <form class="layui-form">
                        <table class="layui-table" lay-even lay-size="sm">
                            <thead>
                                <tr>
                                    <th>渠道名称</th>
                                    <th>排序</th>
                                    <th>当前状态</th>
                                    <th>当日总额</th>
                                    <th>总额</th>
                                    <th>接口地址</th>
                                    <th>手续费</th>
                                    <th>最小金额</th>
                                    <th>最大金额</th>
                                    <th>分类</th>
                                    <th>客户端</th>
                                    <th>管理操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if($data): foreach($data as $key=>$value): ?>
                                <tr>
                                    <td><?php echo htmlentities($value['name']); ?></td>
                                    <td><?php echo htmlentities($value['sort']); ?></td>
                                    <td>
                                        <input type="checkbox" name="state" value="<?php echo htmlentities($value['id']); ?>" lay-skin="switch" lay-text="开|关" lay-filter="recharge-type-status" <?php echo $value['state']==1 ? 'checked'  :  ''; ?>>
                                    </td>
                                    <td><?php echo htmlentities($value['todayTotal']); ?></td>
                                    <td><?php echo htmlentities($value['allTotal']); ?></td>
                                    <td><?php echo htmlentities($value['submitUrl']); ?></td>
                                    <td><?php echo htmlentities($value['fee']); ?></td>
                                    <td><?php echo htmlentities($value['minPrice']); ?></td>
                                    <td><?php echo htmlentities($value['maxPrice']); ?></td>
                                    <td><?php echo htmlentities($value['mode']); ?></td>
                                    <td><?php echo htmlentities($value['type']); ?></td>
                                    <td>
                                        <div class="layui-btn-group">
                                            <button type="button" class="layui-btn layui-btn-xs" onclick="pubedit(this,<?php echo htmlentities($value['id']); ?>,'recharge_channel_edit','编辑充值渠道','50%','70%')">
                                                <i class="layui-icon">&#xe642;</i>
                                            </button>
                                            <button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="pubdel(this,<?php echo htmlentities($value['id']); ?>,'recharge_channel_delete')">
                                                <i class="layui-icon">&#xe640;</i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; else: ?>
                                <tr>
                                    <td colspan="12" style="text-align: center;">暂无数据</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
</body>
</html>