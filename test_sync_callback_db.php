<?php
/**
 * 数据库同步回调处理测试脚本
 * 直接连接数据库测试同步回调处理逻辑
 */

// 数据库配置
$host = 'dianzhan_mysql';
$port = '3306';
$dbname = 'dianzhan';
$username = 'root';
$password = 'root123456';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n";
    
    // 测试1: 模拟JayaPay同步成功回调
    testJayaPaySyncSuccess($pdo);
    
    // 测试2: 模拟JayaPay同步失败回调
    testJayaPaySyncFailed($pdo);
    
    // 测试3: 模拟WatchPay同步成功回调
    testWatchPaySyncSuccess($pdo);
    
    // 测试4: 模拟WatchPay同步失败回调
    testWatchPaySyncFailed($pdo);
    
    echo "\n🎉 所有测试完成！\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

/**
 * 测试JayaPay同步成功回调
 */
function testJayaPaySyncSuccess($pdo) {
    echo "\n=== 测试JayaPay同步成功回调 ===\n";
    
    // 创建测试订单
    $orderNumber = '202508041600002';
    createTestOrder($pdo, $orderNumber, '测试JayaPay同步成功');
    
    // 模拟JayaPay同步成功响应
    $response = [
        'platRespCode' => 'SUCCESS',
        'platRespMessage' => 'Request success',
        'platOrderNum' => 'APF1B17D8358C804001',
        'orderNum' => $orderNumber,
        'status' => '2', // 代付成功
        'statusMsg' => 'Payout Success',
        'money' => '10000',
        'fee' => '1000'
    ];
    
    echo "📝 JayaPay响应: status={$response['status']} (代付成功)\n";
    
    // 模拟同步回调处理逻辑
    if ($response['status'] === '2') {
        // 立即处理成功状态
        $sql = "UPDATE ly_user_withdrawals SET state = 1, success_time = ?, remarks = ? WHERE order_number = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([time(), 'JayaPay同步代付成功', $orderNumber]);
        
        echo "✅ 处理结果: 同步返回代付成功，立即更新订单状态为成功(state=1)\n";
        
        // 验证结果
        $order = getOrderByNumber($pdo, $orderNumber);
        echo "🔍 验证结果: 订单状态={$order['state']}, 备注={$order['remarks']}\n";
    }
}

/**
 * 测试JayaPay同步失败回调
 */
function testJayaPaySyncFailed($pdo) {
    echo "\n=== 测试JayaPay同步失败回调 ===\n";
    
    // 创建测试订单
    $orderNumber = '202508041600003';
    createTestOrder($pdo, $orderNumber, '测试JayaPay同步失败');
    
    // 模拟JayaPay同步失败响应
    $response = [
        'platRespCode' => 'SUCCESS',
        'platRespMessage' => 'Request success',
        'platOrderNum' => 'APF1B17D8358C804002',
        'orderNum' => $orderNumber,
        'status' => '4', // 代付失败
        'statusMsg' => 'Payout Failed',
        'money' => '10000',
        'fee' => '1000'
    ];
    
    echo "📝 JayaPay响应: status={$response['status']} (代付失败)\n";
    
    // 模拟同步回调处理逻辑
    if ($response['status'] === '4') {
        // 立即处理失败状态
        $sql = "UPDATE ly_user_withdrawals SET state = 2, fail_time = ?, fail_reason = ?, remarks = ? WHERE order_number = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([time(), 'JayaPay代付失败，状态：4', 'JayaPay同步代付失败', $orderNumber]);
        
        echo "❌ 处理结果: 同步返回代付失败，立即更新订单状态为失败(state=2)\n";
        
        // 验证结果
        $order = getOrderByNumber($pdo, $orderNumber);
        echo "🔍 验证结果: 订单状态={$order['state']}, 失败原因={$order['fail_reason']}\n";
    }
}

/**
 * 测试WatchPay同步成功回调
 */
function testWatchPaySyncSuccess($pdo) {
    echo "\n=== 测试WatchPay同步成功回调 ===\n";
    
    // 创建测试订单
    $orderNumber = '202508041600004';
    createTestOrder($pdo, $orderNumber, '测试WatchPay同步成功', 7); // channel_id=7 for WatchPay
    
    // 模拟WatchPay同步成功响应
    $response = [
        'respCode' => 'SUCCESS',
        'mchId' => '123456666',
        'merTransferId' => $orderNumber,
        'transferAmount' => '10000',
        'applyDate' => '2025-08-04 16:00:00',
        'tradeNo' => '8801029',
        'tradeResult' => '1', // 转账成功
        'errorMsg' => null
    ];
    
    echo "📝 WatchPay响应: tradeResult={$response['tradeResult']} (转账成功)\n";
    
    // 模拟同步回调处理逻辑
    if ($response['tradeResult'] === '1') {
        // 立即处理成功状态
        $sql = "UPDATE ly_user_withdrawals SET state = 1, success_time = ?, remarks = ? WHERE order_number = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([time(), 'WatchPay同步代付成功', $orderNumber]);
        
        echo "✅ 处理结果: 同步返回转账成功，立即更新订单状态为成功(state=1)\n";
        
        // 验证结果
        $order = getOrderByNumber($pdo, $orderNumber);
        echo "🔍 验证结果: 订单状态={$order['state']}, 备注={$order['remarks']}\n";
    }
}

/**
 * 测试WatchPay同步失败回调
 */
function testWatchPaySyncFailed($pdo) {
    echo "\n=== 测试WatchPay同步失败回调 ===\n";
    
    // 创建测试订单
    $orderNumber = '202508041600005';
    createTestOrder($pdo, $orderNumber, '测试WatchPay同步失败', 7); // channel_id=7 for WatchPay
    
    // 模拟WatchPay同步失败响应
    $response = [
        'respCode' => 'SUCCESS',
        'mchId' => '123456666',
        'merTransferId' => $orderNumber,
        'transferAmount' => '10000',
        'applyDate' => '2025-08-04 16:00:00',
        'tradeNo' => '8801030',
        'tradeResult' => '2', // 转账失败
        'errorMsg' => null
    ];
    
    echo "📝 WatchPay响应: tradeResult={$response['tradeResult']} (转账失败)\n";
    
    // 模拟同步回调处理逻辑
    if ($response['tradeResult'] === '2') {
        // 立即处理失败状态
        $sql = "UPDATE ly_user_withdrawals SET state = 2, fail_time = ?, fail_reason = ?, remarks = ? WHERE order_number = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([time(), 'WatchPay代付失败：转账失败', 'WatchPay同步代付失败', $orderNumber]);
        
        echo "❌ 处理结果: 同步返回转账失败，立即更新订单状态为失败(state=2)\n";
        
        // 验证结果
        $order = getOrderByNumber($pdo, $orderNumber);
        echo "🔍 验证结果: 订单状态={$order['state']}, 失败原因={$order['fail_reason']}\n";
    }
}

/**
 * 创建测试订单
 */
function createTestOrder($pdo, $orderNumber, $remarks, $channelId = 6) {
    $sql = "INSERT INTO ly_user_withdrawals (uid, order_number, bank_id, bank_name, card_number, card_name, price, time, state, channel_id, remarks) 
            VALUES (1150, ?, '19', 'DANA', '************', 'SAPRUDIN', 10000.0000, ?, 4, ?, ?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$orderNumber, time(), $channelId, $remarks]);
    echo "📋 创建测试订单: {$orderNumber}\n";
}

/**
 * 根据订单号获取订单信息
 */
function getOrderByNumber($pdo, $orderNumber) {
    $sql = "SELECT * FROM ly_user_withdrawals WHERE order_number = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$orderNumber]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
