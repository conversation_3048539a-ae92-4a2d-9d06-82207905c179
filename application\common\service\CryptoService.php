<?php
namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 会话级对称密钥交换服务
 * 1. 首次访问获取RSA公钥
 * 2. 前端用公钥加密对称密钥发送给服务器
 * 3. 服务器缓存会话密钥用于后续业务加密
 */
class CryptoService
{
    // 加密算法常量
    private static $cipher = 'AES-256-CBC';
    private static $hashAlgo = 'SHA256';
    private static $opensslFlags = OPENSSL_RAW_DATA;
    
    // 缓存键前缀
    private static $rsaKeyPrefix = 'rsa_keys_';
    private static $sessionKeyPrefix = 'session_key_';
    private static $saltPrefix = 'crypto_salt_';
    
    // 会话密钥有效期（秒）
    private static $sessionKeyTtl = 7200; // 2小时（可根据需求调整：3600=1小时，14400=4小时）
    
    // RSA密钥对有效期（秒）
    private static $rsaKeyTtl = 86400; // 24小时定期更换RSA密钥对
    
    // RSA密钥配置
    private static $rsaConfig = [
        'private_key' => '',
        'public_key' => '',
        'key_size' => 2048
    ];
    
    /**
     * 获取RSA公钥（供前端使用）
     * @return array 包含公钥的响应
     */
    public static function getPublicKeyForClient()
    {
        try {
            $publicKey = self::getRsaPublicKey();
            
            Log::info('客户端请求RSA公钥', [
                'public_key_length' => strlen($publicKey),
                'key_preview' => substr($publicKey, 0, 50) . '...'
            ]);
            
            return [
                'success' => true,
                'public_key' => $publicKey,
                'key_id' => md5($publicKey),
                'expires_in' => self::$sessionKeyTtl
            ];
            
        } catch (\Exception $e) {
            Log::error('获取RSA公钥失败: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => '密钥服务不可用'
            ];
        }
    }
    
    /**
     * 接收并存储会话密钥
     * @param string $encryptedSessionKey RSA加密的会话密钥
     * @param string $sessionId 会话标识
     * @return array 处理结果
     */
    public static function establishSessionKey($encryptedSessionKey, $sessionId)
    {
        try {
            Log::info('开始建立会话密钥', [
                'session_id' => $sessionId,
                'encrypted_key_length' => strlen($encryptedSessionKey)
            ]);
            
            // 1. RSA解密会话密钥
            $sessionKey = self::rsaDecryptAesKey($encryptedSessionKey);
            if (!$sessionKey || strlen($sessionKey) !== 32) {
                throw new \Exception('会话密钥解密失败或格式错误');
            }
            
            // 2. 存储会话密钥到缓存
            $cacheKey = self::$sessionKeyPrefix . $sessionId;
            Cache::set($cacheKey, $sessionKey, self::$sessionKeyTtl);
            
            Log::info('会话密钥建立成功', [
                'session_id' => $sessionId,
                'key_length' => strlen($sessionKey),
                'cache_key' => $cacheKey,
                'ttl' => self::$sessionKeyTtl
            ]);
            
            return [
                'success' => true,
                'session_id' => $sessionId,
                'expires_in' => self::$sessionKeyTtl
            ];
            
        } catch (\Exception $e) {
            Log::error('建立会话密钥失败: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => '密钥交换失败'
            ];
        }
    }
    
    /**
     * 获取会话密钥
     * @param string $sessionId 会话标识
     * @return string|false 会话密钥或失败
     */
    public static function getSessionKey($sessionId)
    {
        $cacheKey = self::$sessionKeyPrefix . $sessionId;
        $sessionKey = Cache::get($cacheKey);
        
        if (!$sessionKey) {
            Log::warning('会话密钥不存在或已过期', ['session_id' => $sessionId]);
            return false;
        }
        
        return $sessionKey;
    }
    
    /**
     * 使用会话密钥加密数据
     * @param mixed $data 要加密的数据
     * @param string $sessionId 会话标识
     * @return array|false 加密结果
     */
    public static function encryptWithSession($data, $sessionId)
    {
        try {
            $sessionKey = self::getSessionKey($sessionId);
            if (!$sessionKey) {
                throw new \Exception('会话密钥无效');
            }
            
            // 生成随机IV
            $iv = random_bytes(16);
            $jsonData = is_string($data) ? $data : json_encode($data, JSON_UNESCAPED_UNICODE);
            
            $encrypted = openssl_encrypt(
                $jsonData,
                self::$cipher,
                $sessionKey,
                self::$opensslFlags,
                $iv
            );
            
            if ($encrypted === false) {
                throw new \Exception('AES加密失败');
            }
            
            return [
                'success' => true,
                'data' => base64_encode($encrypted),
                'iv' => base64_encode($iv),
                'session_id' => $sessionId
            ];
            
        } catch (\Exception $e) {
            Log::error('会话加密失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 使用会话密钥解密数据
     * @param string $encryptedData 加密的数据
     * @param string $iv 初始化向量
     * @param string $sessionId 会话标识
     * @return array 解密结果
     */
    public static function decryptWithSession($encryptedData, $iv, $sessionId)
    {
        try {
            $sessionKey = self::getSessionKey($sessionId);
            if (!$sessionKey) {
                // 返回特定的错误码，提示前端重新建立会话
                return [
                    'success' => false,
                    'error' => 'SESSION_KEY_EXPIRED',
                    'error_msg' => '加密会话已过期，请重新建立',
                    'need_reestablish' => true
                ];
            }
            
            $decrypted = openssl_decrypt(
                base64_decode($encryptedData),
                self::$cipher,
                $sessionKey,
                self::$opensslFlags,
                base64_decode($iv)
            );
            
            if ($decrypted === false) {
                throw new \Exception('AES解密失败');
            }
            
            // 尝试解析为JSON
            $data = json_decode($decrypted, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $data = $data;
            } else {
                $data = $decrypted;
            }
            
            return [
                'success' => true,
                'data' => $data
            ];
            
        } catch (\Exception $e) {
            Log::error('会话解密失败: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 初始化RSA密钥对
     */
    public static function initRsaKeys()
    {
        // Windows环境下的OpenSSL配置
        $config = [
            "digest_alg" => "sha256",
            "private_key_bits" => self::$rsaConfig['key_size'],
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ];

        // 尝试设置OpenSSL配置文件路径（Windows兼容性）
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Windows环境下尝试不同的配置
            $possibleConfigs = [
                $config,
                array_merge($config, ["config" => ""]), // 空配置
                ["private_key_bits" => 1024, "private_key_type" => OPENSSL_KEYTYPE_RSA], // 简化配置
            ];

            $res = false;
            foreach ($possibleConfigs as $testConfig) {
                $res = @openssl_pkey_new($testConfig);
                if ($res !== false) {
                    break;
                }
            }
        } else {
            $res = openssl_pkey_new($config);
        }

        if (!$res) {
            // 如果还是失败，尝试使用预生成的密钥对
            return self::getDefaultKeys();
        }
        
        openssl_pkey_export($res, $privateKey);
        $publicKey = openssl_pkey_get_details($res)['key'];
        
        self::$rsaConfig['private_key'] = $privateKey;
        self::$rsaConfig['public_key'] = $publicKey;
        
        // 缓存密钥对
        Cache::set(self::$rsaKeyPrefix . 'private', $privateKey, 86400);
        Cache::set(self::$rsaKeyPrefix . 'public', $publicKey, 86400);
        
        return [
            'private_key' => $privateKey,
            'public_key' => $publicKey
        ];
    }
    
    /**
     * 获取RSA公钥
     */
    public static function getRsaPublicKey()
    {
        $publicKey = Cache::get(self::$rsaKeyPrefix . 'public');
        if (!$publicKey) {
            $keys = self::initRsaKeys();
            return $keys['public_key'];
        }
        return $publicKey;
    }
    
    /**
     * 获取RSA私钥
     */
    private static function getRsaPrivateKey()
    {
        $privateKey = Cache::get(self::$rsaKeyPrefix . 'private');
        if (!$privateKey) {
            $keys = self::initRsaKeys();
            return $keys['private_key'];
        }
        return $privateKey;
    }
    
    /**
     * RSA解密AES密钥
     * @param string $encryptedKey 加密的AES密钥
     * @return string 解密后的AES密钥
     */
    public static function rsaDecryptAesKey($encryptedKey)
    {
        Log::info('RSA解密：开始解密AES密钥', [
            'encrypted_key_length' => strlen($encryptedKey)
        ]);

        try {
            $privateKey = self::getRsaPrivateKey();
            $keyResource = openssl_pkey_get_private($privateKey);

            if (!$keyResource) {
                Log::error('RSA解密：私钥无效');
                throw new \Exception('RSA私钥无效');
            }

            Log::info('RSA解密：私钥加载成功');
            
            $decryptedKey = '';
            $result = openssl_private_decrypt(
                base64_decode($encryptedKey),
                $decryptedKey,
                $keyResource
            );

            openssl_free_key($keyResource);

            if (!$result) {
                Log::error('RSA解密：openssl_private_decrypt失败', [
                    'openssl_error' => openssl_error_string()
                ]);
                throw new \Exception('RSA解密失败');
            }

            Log::info('✅ RSA解密：AES密钥解密成功');

            // 检查密钥长度和修复UTF-8编码问题
            Log::info('🔍 RSA解密结果 - 密钥长度: ' . strlen($decryptedKey) . ' (期望32)');
            Log::info('🔍 RSA解密结果 - 密钥完整十六进制: ' . bin2hex($decryptedKey));
            Log::info('🔍 RSA解密结果 - 密钥长度检查: ' . (strlen($decryptedKey) === 32 ? '✅正确' : '❌错误'));
            Log::info('🔍 RSA解密结果 - 是否为二进制: ' . (ctype_print($decryptedKey) ? '❌ (printable)' : '✅二进制数据'));
            Log::info('🔍 RSA解密结果 - 熵检查: ' . (count(array_unique(str_split($decryptedKey))) > 10 ? '✅高熵' : '❌低熵'));

            // 如果密钥长度不是32字节，尝试修复UTF-8编码问题
            if (strlen($decryptedKey) !== 32) {
                Log::info('🔧 检测到密钥长度异常，尝试修复UTF-8编码问题');
                Log::info('🔧 原始密钥长度: ' . strlen($decryptedKey));
                Log::info('🔧 原始密钥十六进制: ' . bin2hex($decryptedKey));
                
                // 尝试修复UTF-8编码导致的问题
                $fixedKey = '';
                $keyBytes = str_split($decryptedKey);
                $i = 0;
                while ($i < count($keyBytes) && strlen($fixedKey) < 32) {
                    $byte = ord($keyBytes[$i]);
                    if ($byte < 128) {
                        // ASCII字符，直接使用
                        $fixedKey .= $keyBytes[$i];
                        $i++;
                    } elseif (($byte >> 5) === 0x06) {
                        // UTF-8 两字节序列
                        if ($i + 1 < count($keyBytes)) {
                            $byte2 = ord($keyBytes[$i + 1]);
                            $unicode = (($byte & 0x1F) << 6) | ($byte2 & 0x3F);
                            $fixedKey .= chr($unicode);
                            $i += 2;
                        } else {
                            $i++;
                        }
                    } elseif (($byte >> 4) === 0x0E) {
                        // UTF-8 三字节序列
                        if ($i + 2 < count($keyBytes)) {
                            $byte2 = ord($keyBytes[$i + 1]);
                            $byte3 = ord($keyBytes[$i + 2]);
                            $unicode = (($byte & 0x0F) << 12) | (($byte2 & 0x3F) << 6) | ($byte3 & 0x3F);
                            $fixedKey .= chr($unicode);
                            $i += 3;
                        } else {
                            $i++;
                        }
                    } else {
                        // 其他情况，直接使用原始字节
                        $fixedKey .= $keyBytes[$i];
                        $i++;
                    }
                }
                
                // 如果修复后长度仍不正确，填充或截断
                if (strlen($fixedKey) < 32) {
                    $fixedKey = str_pad($fixedKey, 32, "\0");
                } elseif (strlen($fixedKey) > 32) {
                    $fixedKey = substr($fixedKey, 0, 32);
                }
                
                Log::info('🔧 修复后密钥长度: ' . strlen($fixedKey));
                Log::info('🔧 修复后密钥十六进制: ' . bin2hex($fixedKey));
                
                if (strlen($fixedKey) === 32) {
                    Log::info('✅ 密钥修复成功');
                    $decryptedKey = $fixedKey;
                } else {
                    Log::error('❌ 密钥修复失败');
                }
            }

            return $decryptedKey;
            
        } catch (\Exception $e) {
            Log::error('RSA解密AES密钥失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * AES解密数据
     * @param string $encryptedData 加密的数据
     * @param string $aesKey AES密钥
     * @param string $iv 初始化向量
     * @return string 解密后的数据
     */
    public static function aesDecrypt($encryptedData, $aesKey, $iv)
    {
        Log::info('AES解密：开始解密数据', [
            'encrypted_data_length' => strlen($encryptedData),
            'aes_key_length' => strlen($aesKey),
            'iv_length' => strlen($iv),
            'cipher' => self::$cipher,
            'key_length_correct' => strlen($aesKey) === 32 ? '✅' : '❌',
            'iv_length_correct' => strlen($iv) === 16 ? '✅' : '❌',
            'aes_key_hex' => bin2hex($aesKey),
            'iv_hex' => bin2hex($iv),
            'encrypted_data_base64_length' => strlen(base64_decode($encryptedData)),
            'openssl_flags' => self::$opensslFlags
        ]);

        try {
            // 添加详细的解密前检查
            $decodedData = base64_decode($encryptedData);
            $decodedIv = base64_decode($iv);

            Log::info('🔍 AES解密前最终检查', [
                'cipher_algorithm' => self::$cipher,
                'key_length_bytes' => strlen($aesKey),
                'key_first_16_hex' => bin2hex(substr($aesKey, 0, 16)),
                'key_last_16_hex' => bin2hex(substr($aesKey, 16, 16)),
                'iv_original' => $iv,
                'iv_decoded_length' => strlen($decodedIv),
                'iv_decoded_hex' => bin2hex($decodedIv),
                'encrypted_data_original_length' => strlen($encryptedData),
                'encrypted_data_decoded_length' => strlen($decodedData),
                'encrypted_data_first_32' => substr($encryptedData, 0, 32),
                'openssl_flags' => self::$opensslFlags,
                'is_base64_data_valid' => $decodedData !== false ? '✅' : '❌',
                'is_base64_iv_valid' => $decodedIv !== false ? '✅' : '❌'
            ]);

            $decrypted = openssl_decrypt(
                $decodedData,
                self::$cipher,
                $aesKey,
                self::$opensslFlags,  // 直接使用常量值
                $decodedIv
            );

            if ($decrypted === false) {
                $opensslError = openssl_error_string();
                Log::error('❌ AES解密：openssl_decrypt失败', [
                    'openssl_error' => $opensslError,
                    'cipher_used' => self::$cipher,
                    'key_length' => strlen($aesKey),
                    'iv_original_length' => strlen($iv),
                    'iv_decoded_length' => strlen($decodedIv),
                    'data_original_length' => strlen($encryptedData),
                    'data_decoded_length' => strlen($decodedData),
                    'key_hex_sample' => bin2hex(substr($aesKey, 0, 8)) . '...',
                    'iv_decoded_hex' => bin2hex($decodedIv),
                    'data_sample' => substr($encryptedData, 0, 50) . '...',
                    'openssl_flags_used' => self::$opensslFlags,
                    'expected_key_length' => 32,
                    'expected_iv_length' => 16,
                    'key_length_ok' => strlen($aesKey) === 32 ? '✅' : '❌',
                    'iv_length_ok' => strlen($decodedIv) === 16 ? '✅' : '❌'
                ]);
                return false;
            }

            Log::info('AES解密：数据解密成功', [
                'decrypted_length' => strlen($decrypted)
            ]);
            
            if ($decrypted === false) {
                throw new \Exception('AES解密失败');
            }
            
            return $decrypted;
            
        } catch (\Exception $e) {
            Log::error('AES解密失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 验证盐值签名
     * @param string $salt 盐值
     * @param string $signature 签名
     * @return bool 验证结果
     */
    public static function verifySaltSignature($salt, $signature)
    {
        try {
            $expectedSignature = hash_hmac(self::$hashAlgo, $salt, self::getSecretKey());
            return hash_equals($expectedSignature, $signature);
            
        } catch (\Exception $e) {
            Log::error('盐值签名验证失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查盐值是否已使用（防重放攻击）
     * @param string $salt 盐值
     * @return bool true=未使用，false=已使用
     */
    public static function checkSaltUnique($salt)
    {
        $cacheKey = self::$saltPrefix . md5($salt);
        
        if (Cache::get($cacheKey)) {
            return false; // 已使用
        }
        
        // 标记为已使用，设置过期时间（5分钟）
        Cache::set($cacheKey, time(), 300);
        return true;
    }
    
    /**
     * 完整的解密流程
     * @param array $requestData 请求数据 ['key', 'data', 'iv', 'salt_signature']
     * @return array|false 解密结果或失败
     */
    public static function decryptRequest($requestData)
    {
        Log::info('=== 开始解密请求数据 ===', [
            'request_fields' => array_keys($requestData),
            'key_length' => isset($requestData['key']) ? strlen($requestData['key']) : 0,
            'data_length' => isset($requestData['data']) ? strlen($requestData['data']) : 0,
            'iv_length' => isset($requestData['iv']) ? strlen($requestData['iv']) : 0,
        ]);

        try {
            // 验证必要参数
            $requiredFields = ['key', 'data', 'iv', 'salt_signature'];
            foreach ($requiredFields as $field) {
                if (!isset($requestData[$field]) || empty($requestData[$field])) {
                    Log::error("解密失败：缺少必要字段", ['missing_field' => $field]);
                    throw new \Exception("缺少必要参数: {$field}");
                }
            }

            Log::info('字段验证通过', ['all_fields_present' => true]);

            // 1. RSA解密AES密钥
            Log::info('🔍 检查key参数类型: ' . gettype($requestData['key']));
            Log::info('🔍 key是否为数组: ' . (is_array($requestData['key']) ? 'YES' : 'NO'));
            Log::info('🔍 key是否为字符串: ' . (is_string($requestData['key']) ? 'YES' : 'NO'));
            if (is_array($requestData['key'])) {
                Log::info('🔍 数组内容: ' . json_encode($requestData['key']));
            } else {
                Log::info('🔍 字符串长度: ' . strlen($requestData['key']));
            }

            Log::info('步骤1：开始RSA解密AES密钥', [
                'encrypted_key_length' => is_string($requestData['key']) ? strlen($requestData['key']) : 'NOT_STRING'
            ]);

            $aesKey = self::rsaDecryptAesKey($requestData['key']);
            if (!$aesKey) {
                Log::error('RSA解密AES密钥失败');
                throw new \Exception('AES密钥解密失败');
            }

            Log::info('步骤1：RSA解密AES密钥成功', [
                'aes_key_length' => strlen($aesKey),
                'expected_length' => 32,
                'length_validation' => strlen($aesKey) === 32 ? '✅ 密钥长度正确' : '❌ 密钥长度错误'
            ]);

            // 2. AES解密数据
            Log::info('步骤2：开始AES解密数据', [
                'encrypted_data_length' => strlen($requestData['data']),
                'iv_length' => strlen($requestData['iv'])
            ]);

            $decryptedData = self::aesDecrypt($requestData['data'], $aesKey, $requestData['iv']);
            if (!$decryptedData) {
                Log::error('AES解密数据失败');
                throw new \Exception('数据解密失败');
            }

            Log::info('步骤2：AES解密数据成功', [
                'decrypted_data_length' => strlen($decryptedData)
            ]);
            
            // 3. 解析解密后的数据（假设格式为：原始数据+盐值）
            Log::info('步骤3：开始解析解密后的数据');
            Log::info('🔍 解密后的原始数据内容: ' . $decryptedData);
            Log::info('🔍 解密后的数据长度: ' . strlen($decryptedData));

            $dataArray = json_decode($decryptedData, true);
            if (!$dataArray || !isset($dataArray['salt']) || !isset($dataArray['payload'])) {
                Log::error('解密数据格式错误', [
                    'decrypted_data' => $decryptedData,
                    'json_error' => json_last_error_msg(),
                    'is_valid_json' => json_last_error() === JSON_ERROR_NONE
                ]);
                throw new \Exception('解密数据格式错误');
            }

            $salt = $dataArray['salt'];
            $payload = $dataArray['payload'];

            Log::info('步骤3：数据解析成功');
            Log::info('🔍 解析后的盐值: ' . $salt);
            Log::info('🔍 解析后的payload类型: ' . gettype($payload));
            Log::info('🔍 解析后的payload内容: ' . (is_array($payload) ? json_encode($payload, JSON_UNESCAPED_UNICODE) : $payload));
            
            if (is_array($payload)) {
                Log::info('🔍 Payload字段详情:');
                foreach ($payload as $key => $value) {
                    Log::info("    [{$key}]: {$value}");
                }
            }

            // 4. 验证盐值签名
            Log::info('步骤4：开始验证盐值签名', [
                'salt_full' => $salt,
                'signature_received' => $requestData['salt_signature'],
                'signature_preview' => substr($requestData['salt_signature'], 0, 16) . '...'
            ]);

            if (!self::verifySaltSignature($salt, $requestData['salt_signature'])) {
                Log::error('盐值签名验证失败');
                throw new \Exception('盐值签名验证失败');
            }

            Log::info('步骤4：盐值签名验证成功');

            // 5. 检查盐值唯一性（防重放）
            Log::info('步骤5：开始检查盐值唯一性');

            if (!self::checkSaltUnique($salt)) {
                Log::error('检测到重放攻击', ['salt' => substr($salt, 0, 8) . '...']);
                throw new \Exception('请求重放攻击检测');
            }

            Log::info('步骤5：盐值唯一性检查通过');
            
            // 记录成功日志
            Log::info('🎉 请求解密成功！完整解密结果：', [
                'salt_value' => $salt,
                'decrypted_payload' => $payload,
                'payload_type' => gettype($payload),
                'payload_size' => is_string($payload) ? strlen($payload) : strlen(json_encode($payload)),
                'final_result_preview' => is_array($payload) ?
                    '数组包含 ' . count($payload) . ' 个字段: ' . implode(', ', array_keys($payload)) :
                    (is_string($payload) ? substr($payload, 0, 100) . '...' : $payload)
            ]);
            
            return [
                'success' => true,
                'data' => $payload,
                'salt' => $salt
            ];
            
        } catch (\Exception $e) {
            Log::error('请求解密失败: ' . $e->getMessage(), $requestData);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * AES加密数据（用于响应）
     * @param mixed $data 要加密的数据
     * @param string $aesKey AES密钥
     * @return array 加密结果
     */
    public static function aesEncrypt($data, $aesKey = null)
    {
        try {
            if (!$aesKey) {
                $aesKey = self::generateAesKey();
            }
            
            $iv = self::generateIv();
            $jsonData = is_string($data) ? $data : json_encode($data);
            
            $encrypted = openssl_encrypt(
                $jsonData,
                self::$cipher,
                $aesKey,
                self::$opensslFlags,  // 直接使用常量值
                $iv
            );
            
            if ($encrypted === false) {
                throw new \Exception('AES加密失败');
            }
            
            return [
                'data' => base64_encode($encrypted),
                'iv' => base64_encode($iv),
                'key' => $aesKey
            ];
            
        } catch (\Exception $e) {
            Log::error('AES加密失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成AES密钥
     */
    public static function generateAesKey()
    {
        return random_bytes(32); // 32字节的原始数据，用于AES-256-CBC
    }
    
    /**
     * 生成初始化向量
     */
    public static function generateIv()
    {
        return random_bytes(16); // 16字节的随机数据
    }
    
    /**
     * 生成盐值
     */
    public static function generateSalt()
    {
        return bin2hex(random_bytes(16)) . '_' . time() . '_' . mt_rand(1000, 9999);
    }
    
    /**
     * 获取系统密钥（用于HMAC）
     */
    private static function getSecretKey()
    {
        // 这里应该从配置文件或环境变量获取
        return 'your_secret_key_here_' . date('Y-m-d');
    }
    
    /**
     * RSA加密AES密钥（用于响应）
     * @param string $aesKey AES密钥
     * @return string 加密后的密钥
     */
    public static function rsaEncryptAesKey($aesKey)
    {
        try {
            $publicKey = self::getRsaPublicKey();
            $keyResource = openssl_pkey_get_public($publicKey);

            if (!$keyResource) {
                throw new \Exception('RSA公钥无效');
            }

            $encryptedKey = '';
            $result = openssl_public_encrypt(
                $aesKey,
                $encryptedKey,
                $keyResource
            );

            openssl_free_key($keyResource);

            if (!$result) {
                throw new \Exception('RSA加密失败');
            }

            return base64_encode($encryptedKey);

        } catch (\Exception $e) {
            Log::error('RSA加密AES密钥失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 完整的加密响应流程
     * @param mixed $responseData 响应数据
     * @return array|false 加密结果或失败
     */
    public static function encryptResponse($responseData)
    {
        try {
            // 1. 生成盐值
            $salt = self::generateSalt();

            // 2. 构造要加密的数据
            $payload = [
                'salt' => $salt,
                'payload' => $responseData,
                'timestamp' => time()
            ];

            // 3. AES加密数据
            $aesResult = self::aesEncrypt(json_encode($payload));
            if (!$aesResult) {
                throw new \Exception('AES加密失败');
            }

            // 4. RSA加密AES密钥
            $encryptedAesKey = self::rsaEncryptAesKey($aesResult['key']);
            if (!$encryptedAesKey) {
                throw new \Exception('RSA加密AES密钥失败');
            }

            // 5. 生成盐值签名
            $saltSignature = hash_hmac(self::$hashAlgo, $salt, self::getSecretKey());

            return [
                'key' => $encryptedAesKey,
                'data' => $aesResult['data'],
                'iv' => $aesResult['iv'],
                'salt_signature' => $saltSignature
            ];

        } catch (\Exception $e) {
            Log::error('响应加密失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 数据签名验证
     * @param array $data 数据
     * @param string $signature 签名
     * @param string $key 密钥
     * @return bool 验证结果
     */
    public static function verifyDataSignature($data, $signature, $key = null)
    {
        try {
            if (!$key) {
                $key = self::getSecretKey();
            }

            $dataString = is_array($data) ? json_encode($data) : $data;
            $expectedSignature = hash_hmac(self::$hashAlgo, $dataString, $key);

            return hash_equals($expectedSignature, $signature);

        } catch (\Exception $e) {
            Log::error('数据签名验证失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成数据签名
     * @param mixed $data 数据
     * @param string $key 密钥
     * @return string 签名
     */
    public static function generateDataSignature($data, $key = null)
    {
        try {
            if (!$key) {
                $key = self::getSecretKey();
            }

            $dataString = is_array($data) ? json_encode($data) : $data;
            return hash_hmac(self::$hashAlgo, $dataString, $key);

        } catch (\Exception $e) {
            Log::error('数据签名生成失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 混淆方法名映射
     */
    public static function __callStatic($name, $arguments)
    {
        $methodMap = [
            'x1' => 'decryptRequest',
            'x2' => 'aesEncrypt',
            'x3' => 'generateSalt',
            'x4' => 'getRsaPublicKey',
            'x5' => 'encryptResponse',
            'x6' => 'verifyDataSignature',
            'x7' => 'generateDataSignature',
            'x8' => 'rsaEncryptAesKey'
        ];

        if (isset($methodMap[$name])) {
            return call_user_func_array([self::class, $methodMap[$name]], $arguments);
        }

        throw new \Exception("未知方法: {$name}");
    }
}
