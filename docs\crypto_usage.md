# RSA+AES混合加密系统使用说明

## 系统架构

本系统实现了完整的RSA+AES混合加密架构，支持前后端安全通信和防重放攻击。

### 加密流程

```
[Vue 前端]
↓
1. 生成 AES 密钥 + IV + 盐（salt）
↓
2. 原始数据 + 盐 → AES 加密
↓
3. AES 密钥 → 用 RSA 公钥加密
↓
4. 发送：{ key, data, iv, salt_signature }
↓
[PHP 后端]
↓
5. 用私钥解密 key → 得到 AES 密钥
↓
6. 用 AES 密钥解密 data → 得到明文 + salt
↓
7. 验证 salt_signature 是否匹配
↓
8. 验证 salt 是否唯一 / 未过期（防重放）
↓
9. 数据验签、处理业务逻辑
```

## 核心文件说明

### 1. CryptoService.php
- 位置：`application/common/service/CryptoService.php`
- 功能：统一加解密服务类，支持代码混淆
- 主要方法：
  - `x1()` → `decryptRequest()` - 解密请求
  - `x2()` → `aesEncrypt()` - AES加密
  - `x3()` → `generateSalt()` - 生成盐值
  - `x4()` → `getRsaPublicKey()` - 获取RSA公钥
  - `x5()` → `encryptResponse()` - 加密响应

### 2. CryptoMiddleware.php
- 位置：`application/common/middleware/CryptoMiddleware.php`
- 功能：自动处理加密请求和响应的中间件

### 3. CryptoController.php
- 位置：`application/api/controller/CryptoController.php`
- 功能：加密API控制器示例

### 4. crypto.php
- 位置：`config/crypto.php`
- 功能：加密系统配置文件

## API接口说明

### 1. 获取RSA公钥
```
GET /api/crypto/public-key

响应：
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "public_key": "-----BEGIN PUBLIC KEY-----\n...",
        "timestamp": 1234567890
    }
}
```

### 2. 测试加密通信
```
POST /api/crypto/test

请求参数：
{
    "key": "RSA加密的AES密钥(base64)",
    "data": "AES加密的数据(base64)",
    "iv": "初始化向量(base64)",
    "salt_signature": "盐值签名"
}

响应：
{
    "code": 1,
    "msg": "处理成功",
    "encrypted": true,
    "key": "RSA加密的AES密钥",
    "data": "AES加密的响应数据",
    "iv": "初始化向量",
    "salt_signature": "盐值签名"
}
```

### 3. 加密登录
```
POST /api/crypto/login

请求参数：同上，data字段解密后应包含：
{
    "username": "用户名",
    "password": "密码"
}
```

## 前端集成示例

### 1. 获取公钥
```javascript
// 获取RSA公钥
async function getPublicKey() {
    const response = await fetch('/api/crypto/public-key');
    const result = await response.json();
    return result.data.public_key;
}
```

### 2. 加密请求数据
```javascript
// 加密请求数据
async function encryptRequest(data) {
    // 1. 获取RSA公钥
    const publicKey = await getPublicKey();
    
    // 2. 生成AES密钥和IV
    const aesKey = generateAESKey(); // 32字节随机密钥
    const iv = generateIV(); // 16字节随机IV
    const salt = generateSalt(); // 随机盐值
    
    // 3. 构造要加密的数据
    const payload = {
        salt: salt,
        payload: data
    };
    
    // 4. AES加密数据
    const encryptedData = aesEncrypt(JSON.stringify(payload), aesKey, iv);
    
    // 5. RSA加密AES密钥
    const encryptedKey = rsaEncrypt(aesKey, publicKey);
    
    // 6. 生成盐值签名
    const saltSignature = generateSaltSignature(salt);
    
    return {
        key: encryptedKey,
        data: encryptedData,
        iv: base64Encode(iv),
        salt_signature: saltSignature
    };
}
```

### 3. 解密响应数据
```javascript
// 解密响应数据
async function decryptResponse(response) {
    if (!response.encrypted) {
        return response.data;
    }
    
    // 1. RSA解密AES密钥
    const aesKey = rsaDecrypt(response.key, privateKey);
    
    // 2. AES解密数据
    const decryptedData = aesDecrypt(response.data, aesKey, response.iv);
    
    // 3. 解析JSON数据
    const result = JSON.parse(decryptedData);
    
    return result.payload;
}
```

## 配置说明

### 环境变量配置
```bash
# RSA密钥对（生产环境建议使用固定密钥）
RSA_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n..."
RSA_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\n..."

# 系统密钥（用于HMAC签名）
CRYPTO_SECRET_KEY="your_secret_key_here"
```

### 配置文件修改
在 `config/crypto.php` 中可以修改：
- RSA密钥长度
- AES加密算法
- 签名算法
- 缓存配置
- 安全配置

## 安全特性

### 1. 防重放攻击
- 每个请求包含唯一盐值
- 服务端缓存已使用的盐值
- 盐值有效期为5分钟

### 2. 数据完整性
- 使用HMAC-SHA256验证数据完整性
- 盐值签名防止篡改

### 3. 代码混淆
- 方法名混淆：`x1`, `x2`, `x3`等
- 常量名混淆：`a1`, `a2`, `a3`等
- 支持自定义混淆映射

### 4. 密钥管理
- RSA密钥对自动生成或配置
- 密钥缓存机制
- 支持密钥轮换

## 性能优化

### 1. 缓存策略
- RSA密钥对缓存
- 盐值缓存（防重放）
- 支持Redis缓存

### 2. 数据大小限制
- 默认最大数据大小：1MB
- 可在配置文件中调整

### 3. 错误处理
- 详细的错误日志
- 友好的错误提示
- 错误代码映射

## 部署注意事项

### 1. 生产环境
- 使用固定的RSA密钥对
- 设置强密码的系统密钥
- 启用HTTPS传输
- 配置防火墙和访问控制

### 2. 性能监控
- 监控加密操作耗时
- 监控内存使用情况
- 监控缓存命中率

### 3. 日志管理
- 定期清理加密日志
- 避免记录敏感数据
- 设置日志轮换策略

## 故障排除

### 常见问题
1. **RSA密钥无效**：检查密钥格式和权限
2. **AES解密失败**：检查密钥和IV的正确性
3. **盐值重复**：检查系统时间和随机数生成
4. **签名验证失败**：检查系统密钥配置

### 调试模式
在配置文件中启用详细日志：
```php
'logging' => [
    'enabled' => true,
    'level' => 'debug',
    'log_sensitive_data' => true, // 仅开发环境
],
```
