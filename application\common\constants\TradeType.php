<?php

namespace app\common\constants;

/**
 * 交易类型常量类
 * 用于替换硬编码的交易类型数字，提高代码可读性和维护性
 */
class TradeType
{
    /**
     * 全部
     */
    const ALL = 0;

    /**
     * 用户充值
     */
    const USER_RECHARGE = 1;

    /**
     * 用户提现
     */
    const USER_WITHDRAWAL = 2;

    /**
     * 发布任务
     */
    const PUBLISH_TASK = 3;

    /**
     * 平台抽水
     */
    const PLATFORM_FEE = 4;

    /**
     * 下级返点
     */
    const SUBORDINATE_REBATE = 5;

    /**
     * 完成任务
     */
    const COMPLETE_TASK = 6;

    /**
     * 注册奖励
     */
    const REGISTER_REWARD = 7;

    /**
     * 推广奖励
     */
    const PROMOTION_REWARD = 8;

    /**
     * 购买会员
     */
    const BUY_MEMBERSHIP = 9;

    /**
     * 撤销任务
     */
    const REVOKE_TASK = 10;

    /**
     * 转账转出
     */
    const TRANSFER_OUT = 11;

    /**
     * 转账转入
     */
    const TRANSFER_IN = 12;

    /**
     * 其他
     */
    const OTHER = 13;

    /**
     * 发放红包
     */
    const SEND_REDPACKET = 14;

    /**
     * 领取红包
     */
    const RECEIVE_REDPACKET = 15;

    /**
     * 余额宝
     */
    const YUEBAO = 16;

    /**
     * 签到
     */
    const SIGNIN = 17;

    /**
     * 大转盘
     */
    const WHEEL = 18;

    /**
     * 购买任务
     */
    const BUY_TASK = 19;

    /**
     * 任务返还
     */
    const TASK_REFUND = 20;

    /**
     * 推荐奖励
     */
    const RECOMMEND_REWARD = 21;

    /**
     * VIP升级退费
     */
    const VIP_UPGRADE_REFUND = 22;

    /**
     * 提现退款
     */
    const WITHDRAWAL_REFUND = 23;

    /**
     * 收入类型数组
     * @return array
     */
    public static function getIncomeTypes()
    {
        return [
            self::SUBORDINATE_REBATE,    // 下级返点
            self::COMPLETE_TASK,         // 完成任务
            self::REGISTER_REWARD,       // 注册奖励
            self::PROMOTION_REWARD,      // 推广奖励
            self::REVOKE_TASK,           // 撤销任务
            self::RECEIVE_REDPACKET,     // 领取红包
            self::YUEBAO,                // 余额宝
            self::SIGNIN,                // 签到
            self::WHEEL,                 // 大转盘
            self::TASK_REFUND,           // 任务返还
            self::RECOMMEND_REWARD,      // 推荐奖励
            self::VIP_UPGRADE_REFUND,    // VIP升级退费
            self::WITHDRAWAL_REFUND,     // 提现退款
        ];
    }

    /**
     * 支出类型数组
     * @return array
     */
    public static function getExpenseTypes()
    {
        return [
            self::USER_WITHDRAWAL,       // 用户提现
            self::PUBLISH_TASK,          // 发布任务
            self::PLATFORM_FEE,          // 平台抽水
            self::SEND_REDPACKET,        // 发放红包
            self::BUY_MEMBERSHIP,        // 购买会员
            self::OTHER,                 // 其他
            self::BUY_TASK,              // 购买任务
        ];
    }

    /**
     * 获取所有交易类型名称映射
     * @return array
     */
    public static function getTypeNames()
    {
        return [
            self::ALL => '全部',
            self::USER_RECHARGE => '用户充值',
            self::USER_WITHDRAWAL => '用户提现',
            self::PUBLISH_TASK => '发布任务',
            self::PLATFORM_FEE => '平台抽水',
            self::SUBORDINATE_REBATE => '下级返点',
            self::COMPLETE_TASK => '完成任务',
            self::REGISTER_REWARD => '注册奖励',
            self::PROMOTION_REWARD => '推广奖励',
            self::BUY_MEMBERSHIP => '购买会员',
            self::REVOKE_TASK => '撤销任务',
            self::TRANSFER_OUT => '转账转出',
            self::TRANSFER_IN => '转账转入',
            self::OTHER => '其他',
            self::SEND_REDPACKET => '发放红包',
            self::RECEIVE_REDPACKET => '领取红包',
            self::YUEBAO => '余额宝',
            self::SIGNIN => '签到',
            self::WHEEL => '大转盘',
            self::BUY_TASK => '购买任务',
            self::TASK_REFUND => '任务返还',
            self::RECOMMEND_REWARD => '推荐奖励',
            self::VIP_UPGRADE_REFUND => 'VIP升级退费',
            self::WITHDRAWAL_REFUND => '提现退款',
        ];
    }
}
