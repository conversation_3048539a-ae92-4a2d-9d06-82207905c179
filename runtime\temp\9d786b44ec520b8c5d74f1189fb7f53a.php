<?php /*a:1:{s:61:"/var/www/html/application/manage/view/bank/control_audit.html";i:**********;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>提现审核</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">订单编号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="order_number" value="<?php echo htmlentities($data['order_number']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">提款金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="" value="<?php echo htmlentities($data['price']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">提款信息</label>
                                <div class="layui-input-block">
                                    <input type="text" name="" value="<?php echo htmlentities($data['bank_id']); ?>——<?php echo htmlentities($data['card_name']); ?>——<?php echo htmlentities($data['card_number']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <?php if($data['state'] != 4): ?>
                            <!-- 非待支付状态：显示审核操作 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">处理结果</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="examine" value="1" title="审核通过"<?php if($data['examine'] == 1): ?> checked<?php endif; ?>>
                                    <input type="radio" name="examine" value="2" title="审核未通过"<?php if($data['examine'] == 1): ?> checked<?php endif; ?>>
                                    <input type="radio" name="examine" value="3" title="待审核"<?php if($data['examine'] == 1): ?> checked<?php endif; ?>>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">处理说明</label>
                                <div class="layui-input-block">
                                    <textarea name="remarks" placeholder="处理说明" class="layui-textarea"><?php echo isset($data['remarks']) ? htmlentities($data['remarks']) : ''; ?></textarea>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <?php endif; ?>

                            <!-- 根据订单状态显示不同操作 -->
                            <?php if($data['state'] == 4): ?>
                            <!-- 待支付状态：显示支付操作 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">当前状态</label>
                                <div class="layui-input-block">
                                    <span class="layui-badge layui-bg-orange">待支付</span>
                                    <span class="layui-word-aux">订单已审核通过，等待选择渠道执行支付</span>
                                </div>
                            </div>
                            <div class="layui-form-item" id="payment_channel_div">
                                <label class="layui-form-label">代付渠道</label>
                                <div class="layui-input-block">
                                    <select name="payment_channel_id" lay-verify="required" lay-search="">
                                        <option value="">请选择代付渠道</option>
                                    </select>
                                </div>
                                <div class="layui-form-mid layui-word-aux">选择渠道执行代付</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">支付备注</label>
                                <div class="layui-input-block">
                                    <textarea name="payment_remarks" placeholder="支付备注（可选）" class="layui-textarea"></textarea>
                                </div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="executePayment">执行支付</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                            <?php else: ?>
                            <!-- 其他状态：显示审核操作 -->
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="controlaudit">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 加载代付渠道列表（用于支付操作）
    function loadPaymentChannels() {
        $.get('/manage/withdrawal_channel/getEnabledChannels', function(res) {
            console.log('渠道接口返回:', res);
            if(res.code == 1) {
                var options = '<option value="">请选择代付渠道</option>';
                if(res.data && res.data.length > 0) {
                    $.each(res.data, function(index, item) {
                        options += '<option value="' + item.id + '">' + item.name + ' (' + item.mode + ')</option>';
                    });
                } else {
                    options += '<option value="">暂无可用渠道</option>';
                }
                $('select[name="payment_channel_id"]').html(options);
                form.render('select');
            } else {
                console.error('获取渠道失败:', res);
                $('select[name="payment_channel_id"]').html('<option value="">获取渠道失败</option>');
                form.render('select');
            }
        }, 'json').fail(function(xhr, status, error) {
            console.error('渠道接口请求失败:', error);
            $('select[name="payment_channel_id"]').html('<option value="">接口请求失败</option>');
            form.render('select');
        });
    }

    // 如果是待支付状态，加载渠道列表
    if($('#payment_channel_div').length > 0) {
        loadPaymentChannels();
    }

    // 监听支付表单提交
    form.on('submit(executePayment)', function(data){
        var field = data.field;

        if(!field.payment_channel_id) {
            layer.msg('请选择代付渠道');
            return false;
        }

        // 添加订单号
        field.order_number = '<?php echo htmlentities($data['order_number']); ?>';
        field.channel_id = field.payment_channel_id;
        field.remarks = field.payment_remarks;

        layer.confirm('确定要执行支付吗？', {icon: 3, title:'提示'}, function(index){
            var loadIndex = layer.load(2);
            $.post('/manage/bank/executePayment', field, function(res){
                layer.close(loadIndex);
                if(res == 1) {
                    layer.msg('支付成功', {icon: 1}, function(){
                        parent.location.reload();
                    });
                } else {
                    // 支付失败时关闭弹窗并刷新父页面列表
                    layer.msg(res, {icon: 2}, function(){
                        parent.layer.closeAll();
                        parent.location.reload();
                    });
                }
            });
            layer.close(index);
        });
        return false;
    });
});
</script>
</body>
</html>