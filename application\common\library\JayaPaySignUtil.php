<?php
/**
 * JayaPay RSA签名工具类
 * 基于JayaPay官方文档实现
 */
class JayaPaySignUtil
{
    /**
     * RSA私钥加密（JayaPay使用私钥加密而非签名）
     * @param string $data 待加密数据
     * @param string $privateKey RSA私钥
     * @return string Base64编码的加密结果
     */
    public static function rsaSign($data, $privateKey)
    {
        try {
            // 处理私钥格式
            $privateKey = self::formatPrivateKey($privateKey);
            
            // 创建私钥资源
            $keyResource = openssl_pkey_get_private($privateKey);
            if (!$keyResource) {
                throw new \Exception('Invalid private key format');
            }
            
            // 获取密钥详情用于分块处理
            $keyDetails = openssl_pkey_get_details($keyResource);
            $keySize = $keyDetails['bits'];
            $maxBlock = intval($keySize / 8) - 11; // RSA加密时的最大块大小
            
            $encrypted = '';
            if (strlen($data) <= $maxBlock) {
                // 单块加密 - 使用私钥加密
                $result = openssl_private_encrypt($data, $encrypted, $keyResource);
                if (!$result) {
                    throw new \Exception('RSA private encrypt failed');
                }
            } else {
                // 分块加密
                $offset = 0;
                $encryptedBlocks = [];
                while ($offset < strlen($data)) {
                    $block = substr($data, $offset, $maxBlock);
                    $blockEncrypted = '';
                    $result = openssl_private_encrypt($block, $blockEncrypted, $keyResource);
                    if (!$result) {
                        throw new \Exception('RSA private encrypt failed at block ' . count($encryptedBlocks));
                    }
                    $encryptedBlocks[] = $blockEncrypted;
                    $offset += $maxBlock;
                }
                $encrypted = implode('', $encryptedBlocks);
            }
            
            // 释放资源
            openssl_free_key($keyResource);
            
            return base64_encode($encrypted);
            
        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay RSA sign error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * RSA公钥验签
     * @param string $data 原始数据
     * @param string $signature Base64编码的签名
     * @param string $publicKey RSA公钥（PEM格式）
     * @return bool 验签结果
     */
    public static function rsaVerify($data, $signature, $publicKey)
    {
        try {
            // 处理公钥格式
            $publicKey = self::formatPublicKey($publicKey);
            
            // 创建公钥资源
            $keyResource = openssl_pkey_get_public($publicKey);
            if (!$keyResource) {
                throw new \Exception('Invalid public key format');
            }
            
            // 解码签名
            $signature = base64_decode($signature);
            
            // 执行验签
            $result = openssl_verify($data, $signature, $keyResource, OPENSSL_ALGO_SHA256);
            
            // 释放资源
            openssl_free_key($keyResource);
            
            return $result === 1;
            
        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay RSA verify error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成JayaPay签名字符串
     * 按照JayaPay规则：只有参数值参与签名，Key按ASCII排序
     * @param array $params 参数数组
     * @return string 待签名字符串
     */
    public static function buildSignString($params)
    {
        // 移除签名参数
        unset($params['sign']);
        
        // 过滤空值
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });
        
        // 按Key的ASCII码排序
        ksort($params);
        
        // 只取参数值进行拼接
        $signString = '';
        foreach ($params as $value) {
            $signString .= $value;
        }
        
        return $signString;
    }
    
    /**
     * 格式化私钥
     * @param string $privateKey 私钥字符串
     * @return string 格式化后的私钥
     */
    private static function formatPrivateKey($privateKey)
    {
        // 移除多余的空格和换行
        $privateKey = trim($privateKey);
        
        // 如果没有头尾标识，添加标准格式
        if (strpos($privateKey, '-----BEGIN') === false) {
            $privateKey = "-----BEGIN PRIVATE KEY-----\n" . 
                         chunk_split($privateKey, 64, "\n") . 
                         "-----END PRIVATE KEY-----";
        }
        
        return $privateKey;
    }
    
    /**
     * 格式化公钥
     * @param string $publicKey 公钥字符串
     * @return string 格式化后的公钥
     */
    private static function formatPublicKey($publicKey)
    {
        // 移除多余的空格和换行
        $publicKey = trim($publicKey);
        
        // 如果没有头尾标识，添加标准格式
        if (strpos($publicKey, '-----BEGIN') === false) {
            $publicKey = "-----BEGIN PUBLIC KEY-----\n" . 
                        chunk_split($publicKey, 64, "\n") . 
                        "-----END PUBLIC KEY-----";
        }
        
        return $publicKey;
    }
    
    /**
     * HTTP POST 请求（专用于JayaPay）
     * @param string $url 请求地址
     * @param array $data 请求数据
     * @param int $timeout 超时时间（秒）
     * @return string 响应内容
     */
    public static function httpPost($url, $data, $timeout = 30)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Content-Encoding: gzip'
            ],
            CURLOPT_USERAGENT => 'JayaPay-PHP-Client/1.0',
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($response === false || !empty($error)) {
            throw new \Exception("HTTP request failed: " . $error);
        }
        
        if ($httpCode !== 200) {
            throw new \Exception("HTTP request failed with code: " . $httpCode);
        }
        
        return $response;
    }
    
    /**
     * 生成订单号
     * @param string $prefix 前缀
     * @return string 订单号
     */
    public static function generateOrderNo($prefix = 'JP')
    {
        return $prefix . date('YmdHis') . mt_rand(100000, 999999);
    }
    
    /**
     * 金额转换（元转分）
     * @param float $amount 金额（元）
     * @return int 金额（分）
     */
    public static function yuanToFen($amount)
    {
        return intval(bcmul($amount, 100, 0));
    }
    
    /**
     * 金额转换（分转元）
     * @param int $amount 金额（分）
     * @return float 金额（元）
     */
    public static function fenToYuan($amount)
    {
        return bcdiv($amount, 100, 2);
    }
    
    /**
     * 验证回调IP
     * @param string $ip 客户端IP
     * @param array $allowedIps 允许的IP列表
     * @return bool 验证结果
     */
    public static function validateNotifyIp($ip, $allowedIps = [])
    {
        if (empty($allowedIps)) {
            return true; // 如果没有配置IP白名单，则不验证
        }
        
        return in_array($ip, $allowedIps);
    }
}
