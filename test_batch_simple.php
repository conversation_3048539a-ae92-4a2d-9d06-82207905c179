<?php
/**
 * 简化的批量代付全链路测试
 * 直接测试批量代付的核心逻辑
 */

// 数据库配置
$host = 'dianzhan_mysql';
$port = '3306';
$dbname = 'dianzhan';
$username = 'root';
$password = 'root123456';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n";
    echo "🚀 开始批量代付全链路测试\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    // 测试批量代付全链路
    testBatchWithdrawalFullChain($pdo);
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎉 批量代付全链路测试完成！\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

/**
 * 测试批量代付全链路
 */
function testBatchWithdrawalFullChain($pdo) {
    echo "\n=== 批量代付全链路测试 ===\n";
    
    // 1. 创建测试订单
    $testOrders = [
        ['order_number' => '202508042200001', 'scenario' => 'JayaPay同步成功', 'channel_id' => 6, 'mock_response' => 'jayapay_success'],
        ['order_number' => '202508042200002', 'scenario' => 'JayaPay同步失败', 'channel_id' => 6, 'mock_response' => 'jayapay_failed'],
        ['order_number' => '202508042200003', 'scenario' => 'JayaPay同步待处理', 'channel_id' => 6, 'mock_response' => 'jayapay_pending'],
        ['order_number' => '202508042200004', 'scenario' => 'WatchPay同步成功', 'channel_id' => 7, 'mock_response' => 'watchpay_success'],
        ['order_number' => '202508042200005', 'scenario' => 'WatchPay同步失败', 'channel_id' => 7, 'mock_response' => 'watchpay_failed'],
    ];
    
    $orderIds = [];
    foreach ($testOrders as $order) {
        $orderId = createTestOrder($pdo, $order['order_number'], $order['scenario']);
        $orderIds[] = $orderId;
        echo "📋 创建测试订单: {$order['order_number']} (ID: {$orderId}) - {$order['scenario']}\n";
    }
    
    // 2. 执行批量代付核心逻辑
    echo "\n🔄 开始执行批量代付核心逻辑...\n";
    
    $successCount = 0;
    $failCount = 0;
    
    foreach ($testOrders as $index => $testOrder) {
        $orderId = $orderIds[$index];
        
        echo "\n--- 处理订单: {$testOrder['order_number']} ---\n";
        
        // 获取订单信息
        $order = getOrderById($pdo, $orderId);
        echo "🔍 订单信息: 状态={$order['state']}, 金额={$order['price']}\n";
        
        // 获取渠道信息
        $channel = getChannelById($pdo, $testOrder['channel_id']);
        echo "🔍 渠道信息: {$channel['name']} (ID: {$channel['id']})\n";
        
        // 更新订单渠道和状态为代付中
        updateOrderChannel($pdo, $orderId, $testOrder['channel_id']);
        updateOrderState($pdo, $orderId, 5, time()); // 5=代付中
        
        // 模拟调用代付服务并获取响应
        $mockResponse = getMockResponse($testOrder['mock_response'], $testOrder['order_number']);
        echo "📝 模拟代付服务响应: " . json_encode($mockResponse, JSON_UNESCAPED_UNICODE) . "\n";
        
        // 模拟代付服务处理逻辑
        $result = processWithdrawalService($pdo, $mockResponse, $testOrder['channel_id'], $testOrder['order_number']);
        echo "🔄 代付服务处理结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        
        // 这里是我修改的批量代付控制器核心逻辑
        if ($result['code'] == 1) {
            // 检查是否已经同步处理完成（JayaPay/WatchPay可能同步返回最终状态）
            $currentOrder = getOrderById($pdo, $orderId);
            
            if ($currentOrder['state'] == 1) {
                // 订单已经同步处理为成功状态
                $successCount++;
                echo "✅ 订单已经同步处理为成功状态\n";
                echo "📝 日志: 批量代付订单ID:{$orderId}（订单号：{$order['order_number']}，金额：￥{$order['price']}，渠道：{$channel['name']}）- 同步成功\n";
            } else {
                // 代付请求成功但未同步完成，保持代付中状态
                $successCount++;
                echo "⏳ 代付请求成功但未同步完成，保持代付中状态\n";
                echo "📝 日志: 批量代付订单ID:{$orderId}（订单号：{$order['order_number']}，金额：￥{$order['price']}，渠道：{$channel['name']}）\n";
            }
        } else {
            $failCount++;
            echo "❌ 代付失败: {$result['msg']}\n";
        }
        
        // 显示最终订单状态
        $finalOrder = getOrderById($pdo, $orderId);
        echo "📊 最终订单状态: {$finalOrder['state']}, 成功时间: {$finalOrder['success_time']}, 失败时间: {$finalOrder['fail_time']}\n";
        echo "📝 最终备注: {$finalOrder['remarks']}\n";
    }
    
    echo "\n📊 批量代付总结: 成功={$successCount}条, 失败={$failCount}条, 总计=" . count($testOrders) . "条\n";
    
    // 3. 验证结果
    echo "\n🔍 验证测试结果:\n";
    validateTestResults($pdo, $testOrders, $orderIds);
    
    // 4. 清理测试数据
    cleanupTestData($pdo);
}

/**
 * 模拟代付服务处理逻辑
 */
function processWithdrawalService($pdo, $mockResponse, $channelId, $orderNumber) {
    if ($channelId == 6) {
        // JayaPay处理逻辑
        return processJayaPayMockResponse($pdo, $mockResponse, $orderNumber);
    } elseif ($channelId == 7) {
        // WatchPay处理逻辑
        return processWatchPayMockResponse($pdo, $mockResponse, $orderNumber);
    }
    
    return ['code' => 0, 'msg' => '不支持的渠道'];
}

/**
 * 处理JayaPay模拟响应
 */
function processJayaPayMockResponse($pdo, $response, $orderNumber) {
    if ($response && isset($response['platRespCode']) && $response['platRespCode'] == 'SUCCESS') {
        echo "✅ JayaPay请求成功\n";
        
        // 处理同步回调状态 - 这是我修改的核心逻辑
        $status = $response['status'] ?? '';
        if ($status === '2') {
            // 同步返回代付成功，立即处理成功状态
            $withdrawal = getOrderByNumber($pdo, $orderNumber);
            if ($withdrawal) {
                processWithdrawalSuccess($pdo, $withdrawal);
                echo "✅ 同步返回代付成功，立即处理成功状态\n";
                echo "📝 日志: JayaPay withdrawal sync success processed: {$orderNumber}\n";
                return ['code' => 1, 'msg' => '代付成功', 'data' => $response];
            }
        } elseif ($status === '4') {
            // 同步返回代付失败，立即处理失败状态
            $withdrawal = getOrderByNumber($pdo, $orderNumber);
            if ($withdrawal) {
                processWithdrawalFailed($pdo, $withdrawal, 'JayaPay代付失败，状态：' . $status);
                echo "❌ 同步返回代付失败，立即处理失败状态\n";
                echo "📝 日志: JayaPay withdrawal sync failed processed: {$orderNumber}\n";
                return ['code' => 0, 'msg' => 'JayaPay代付失败，状态：' . $status];
            }
        } else {
            // 其他状态（0=待处理，1=已受理，5=银行代付中），等待异步回调
            echo "⏳ 其他状态({$status})，等待异步回调\n";
            echo "📝 日志: JayaPay withdrawal sync status {$status}, waiting for async callback: {$orderNumber}\n";
        }
        
        return ['code' => 1, 'msg' => '代付请求已提交', 'data' => $response];
    } else {
        $errorMsg = $response['platRespMessage'] ?? '代付请求失败';
        echo "❌ JayaPay请求失败: {$errorMsg}\n";
        return ['code' => 0, 'msg' => $errorMsg];
    }
}

/**
 * 处理WatchPay模拟响应
 */
function processWatchPayMockResponse($pdo, $response, $orderNumber) {
    if ($response && isset($response['respCode']) && $response['respCode'] == 'SUCCESS') {
        echo "✅ WatchPay请求成功\n";
        
        // 处理同步回调状态 - 这是我修改的核心逻辑
        $tradeResult = $response['tradeResult'] ?? '';
        if ($tradeResult === '1') {
            // 同步返回转账成功，立即处理成功状态
            $withdrawal = getOrderByNumber($pdo, $orderNumber);
            if ($withdrawal) {
                processWithdrawalSuccess($pdo, $withdrawal);
                echo "✅ 同步返回转账成功，立即处理成功状态\n";
                echo "📝 日志: WatchPay withdrawal sync success processed: {$orderNumber}\n";
                return ['code' => 1, 'msg' => '代付成功', 'data' => $response];
            }
        } elseif ($tradeResult === '2' || $tradeResult === '3') {
            // 同步返回转账失败或拒绝，立即处理失败状态
            $withdrawal = getOrderByNumber($pdo, $orderNumber);
            if ($withdrawal) {
                $failureReason = $tradeResult === '2' ? '转账失败' : '转账拒绝';
                processWithdrawalFailed($pdo, $withdrawal, 'WatchPay代付失败：' . $failureReason);
                echo "❌ 同步返回{$failureReason}，立即处理失败状态\n";
                echo "📝 日志: WatchPay withdrawal sync failed processed: {$orderNumber}, reason: {$failureReason}\n";
                return ['code' => 0, 'msg' => 'WatchPay代付失败：' . $failureReason];
            }
        } else {
            // 其他状态（0=申请成功，4=处理中），等待异步回调
            echo "⏳ 其他状态({$tradeResult})，等待异步回调\n";
            echo "📝 日志: WatchPay withdrawal sync status {$tradeResult}, waiting for async callback: {$orderNumber}\n";
        }
        
        return ['code' => 1, 'msg' => '代付请求已提交', 'data' => $response];
    } else {
        $errorMsg = $response['errorMsg'] ?? '代付请求失败';
        echo "❌ WatchPay请求失败: {$errorMsg}\n";
        return ['code' => 0, 'msg' => $errorMsg];
    }
}

/**
 * 获取模拟响应数据
 */
function getMockResponse($responseType, $orderNumber) {
    $responses = [
        'jayapay_success' => [
            'platRespCode' => 'SUCCESS',
            'platRespMessage' => 'Request success',
            'platOrderNum' => 'APF' . time(),
            'orderNum' => $orderNumber,
            'status' => '2', // 代付成功
            'statusMsg' => 'Payout Success',
            'money' => '10000',
            'fee' => '1000'
        ],
        'jayapay_failed' => [
            'platRespCode' => 'SUCCESS',
            'platRespMessage' => 'Request success',
            'platOrderNum' => 'APF' . time(),
            'orderNum' => $orderNumber,
            'status' => '4', // 代付失败
            'statusMsg' => 'Payout Failed',
            'money' => '10000',
            'fee' => '1000'
        ],
        'jayapay_pending' => [
            'platRespCode' => 'SUCCESS',
            'platRespMessage' => 'Request success',
            'platOrderNum' => 'APF' . time(),
            'orderNum' => $orderNumber,
            'status' => '0', // 待处理
            'statusMsg' => 'Apply',
            'money' => '10000',
            'fee' => '1000'
        ],
        'watchpay_success' => [
            'respCode' => 'SUCCESS',
            'mchId' => '123456666',
            'merTransferId' => $orderNumber,
            'transferAmount' => '10000',
            'applyDate' => date('Y-m-d H:i:s'),
            'tradeNo' => '88' . time(),
            'tradeResult' => '1', // 转账成功
            'errorMsg' => null
        ],
        'watchpay_failed' => [
            'respCode' => 'SUCCESS',
            'mchId' => '123456666',
            'merTransferId' => $orderNumber,
            'transferAmount' => '10000',
            'applyDate' => date('Y-m-d H:i:s'),
            'tradeNo' => '88' . time(),
            'tradeResult' => '2', // 转账失败
            'errorMsg' => null
        ]
    ];
    
    return $responses[$responseType] ?? null;
}

/**
 * 验证测试结果
 */
function validateTestResults($pdo, $testOrders, $orderIds) {
    foreach ($testOrders as $index => $testOrder) {
        $orderId = $orderIds[$index];
        $order = getOrderById($pdo, $orderId);
        
        echo "订单 {$testOrder['order_number']} ({$testOrder['scenario']}):\n";
        echo "  - 最终状态: {$order['state']}\n";
        echo "  - 成功时间: {$order['success_time']}\n";
        echo "  - 失败时间: {$order['fail_time']}\n";
        echo "  - 失败原因: {$order['fail_reason']}\n";
        echo "  - 备注: {$order['remarks']}\n";
        
        // 验证预期结果
        $isValid = false;
        if (strpos($testOrder['mock_response'], 'success') !== false) {
            $isValid = ($order['state'] == 1 && $order['success_time'] > 0);
            echo "  " . ($isValid ? "✅" : "❌") . " 同步成功处理" . ($isValid ? "正确" : "异常") . "\n";
        } elseif (strpos($testOrder['mock_response'], 'failed') !== false) {
            $isValid = ($order['state'] == 2 && $order['fail_time'] > 0);
            echo "  " . ($isValid ? "✅" : "❌") . " 同步失败处理" . ($isValid ? "正确" : "异常") . "\n";
        } else {
            $isValid = ($order['state'] == 5);
            echo "  " . ($isValid ? "✅" : "❌") . " 同步待处理状态" . ($isValid ? "正确" : "异常") . "\n";
        }
        
        echo "\n";
    }
}

// 辅助函数
function createTestOrder($pdo, $orderNumber, $remarks) {
    $sql = "INSERT INTO ly_user_withdrawals (uid, order_number, bank_id, bank_name, card_number, card_name, price, time, state, channel_id, remarks) 
            VALUES (1150, ?, '19', 'DANA', '************', 'SAPRUDIN', 10000.0000, ?, 4, 0, ?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$orderNumber, time(), $remarks]);
    return $pdo->lastInsertId();
}

function getOrderById($pdo, $id) {
    $sql = "SELECT * FROM ly_user_withdrawals WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getOrderByNumber($pdo, $orderNumber) {
    $sql = "SELECT * FROM ly_user_withdrawals WHERE order_number = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$orderNumber]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getChannelById($pdo, $channelId) {
    $sql = "SELECT * FROM ly_withdrawal_channel WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$channelId]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function updateOrderChannel($pdo, $orderId, $channelId) {
    $sql = "UPDATE ly_user_withdrawals SET channel_id = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$channelId, $orderId]);
}

function updateOrderState($pdo, $orderId, $state, $processTime) {
    $sql = "UPDATE ly_user_withdrawals SET state = ?, process_time = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$state, $processTime, $orderId]);
}

function processWithdrawalSuccess($pdo, $withdrawal) {
    $sql = "UPDATE ly_user_withdrawals SET state = 1, success_time = ?, remarks = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([time(), '同步代付成功', $withdrawal['id']]);
}

function processWithdrawalFailed($pdo, $withdrawal, $reason) {
    $sql = "UPDATE ly_user_withdrawals SET state = 2, fail_time = ?, fail_reason = ?, remarks = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([time(), $reason, '同步代付失败', $withdrawal['id']]);
}

function cleanupTestData($pdo) {
    $sql = "DELETE FROM ly_user_withdrawals WHERE order_number LIKE '202508042200%'";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    echo "🧹 测试数据已清理\n";
}
