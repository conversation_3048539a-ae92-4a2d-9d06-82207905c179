<?php
echo "测试RSA密钥生成...\n";

// 检查OpenSSL版本
echo "OpenSSL版本: " . OPENSSL_VERSION_TEXT . "\n";

// 测试基本的RSA密钥生成
$config = [
    "digest_alg" => "sha256",
    "private_key_bits" => 2048,
    "private_key_type" => OPENSSL_KEYTYPE_RSA,
];

echo "开始生成RSA密钥对...\n";
$res = openssl_pkey_new($config);

if (!$res) {
    echo "RSA密钥生成失败！\n";
    echo "OpenSSL错误: " . openssl_error_string() . "\n";
    
    // 尝试更简单的配置
    echo "尝试简化配置...\n";
    $simpleConfig = [
        "private_key_bits" => 1024,
        "private_key_type" => OPENSSL_KEYTYPE_RSA,
    ];
    
    $res = openssl_pkey_new($simpleConfig);
    if (!$res) {
        echo "简化配置也失败: " . openssl_error_string() . "\n";
        exit(1);
    } else {
        echo "简化配置成功！\n";
    }
} else {
    echo "RSA密钥生成成功！\n";
}

// 导出私钥
$privateKey = '';
$result = openssl_pkey_export($res, $privateKey);
if (!$result) {
    echo "私钥导出失败: " . openssl_error_string() . "\n";
} else {
    echo "私钥导出成功，长度: " . strlen($privateKey) . "\n";
}

// 获取公钥
$publicKeyDetails = openssl_pkey_get_details($res);
if (!$publicKeyDetails) {
    echo "公钥获取失败: " . openssl_error_string() . "\n";
} else {
    echo "公钥获取成功，长度: " . strlen($publicKeyDetails['key']) . "\n";
}

// 测试简单的加密解密
$testData = "Hello World";
echo "测试数据: $testData\n";

// 公钥加密
$encrypted = '';
$encryptResult = openssl_public_encrypt($testData, $encrypted, $publicKeyDetails['key']);
if (!$encryptResult) {
    echo "公钥加密失败: " . openssl_error_string() . "\n";
} else {
    echo "公钥加密成功，密文长度: " . strlen($encrypted) . "\n";
    
    // 私钥解密
    $decrypted = '';
    $decryptResult = openssl_private_decrypt($encrypted, $decrypted, $res);
    if (!$decryptResult) {
        echo "私钥解密失败: " . openssl_error_string() . "\n";
    } else {
        echo "私钥解密成功: $decrypted\n";
        echo "数据一致性: " . ($testData === $decrypted ? '✓' : '✗') . "\n";
    }
}

// 释放资源
openssl_free_key($res);

echo "RSA测试完成！\n";
?>
