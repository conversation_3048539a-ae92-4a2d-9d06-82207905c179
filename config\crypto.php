<?php
/**
 * 加密配置文件
 */
return [
    // RSA配置
    'rsa' => [
        // RSA密钥长度
        'key_size' => 2048,
        
        // RSA密钥缓存时间（秒）
        'cache_time' => 86400,
        
        // 是否自动生成密钥对
        'auto_generate' => true,
        
        // 预设的RSA密钥对（生产环境建议使用固定密钥）
        'private_key' => env('RSA_PRIVATE_KEY', ''),
        'public_key' => env('RSA_PUBLIC_KEY', ''),
    ],
    
    // AES配置
    'aes' => [
        // AES加密算法
        'cipher' => 'AES-256-CBC',
        
        // AES密钥长度（字节）
        'key_length' => 32,
        
        // IV长度（字节）
        'iv_length' => 16,
    ],
    
    // 签名配置
    'signature' => [
        // HMAC算法
        'algorithm' => 'SHA256',
        
        // 系统密钥（建议从环境变量获取）
        'secret_key' => env('CRYPTO_SECRET_KEY', 'default_secret_key_change_in_production'),
        
        // 签名有效期（秒）
        'expire_time' => 300,
    ],
    
    // 防重放攻击配置
    'anti_replay' => [
        // 盐值缓存时间（秒）
        'salt_cache_time' => 300,
        
        // 盐值前缀
        'salt_prefix' => 'crypto_salt_',
        
        // 是否启用防重放检查
        'enabled' => true,
    ],
    
    // 代码混淆配置
    'obfuscation' => [
        // 是否启用方法名混淆
        'method_obfuscation' => true,
        
        // 混淆方法映射
        'method_map' => [
            'decrypt_request' => 'x1',
            'encrypt_data' => 'x2',
            'generate_salt' => 'x3',
            'get_public_key' => 'x4',
            'encrypt_response' => 'x5',
            'verify_signature' => 'x6',
            'generate_signature' => 'x7',
            'encrypt_key' => 'x8',
        ],
        
        // 混淆常量映射
        'constant_map' => [
            'AES_CIPHER' => 'a1',
            'HASH_ALGO' => 'a2',
            'OPENSSL_FLAG' => 'a3',
            'SALT_PREFIX' => 'a4',
            'KEY_PREFIX' => 'a5',
        ],
    ],
    
    // 日志配置
    'logging' => [
        // 是否记录加密操作日志
        'enabled' => true,
        
        // 日志级别
        'level' => 'info',
        
        // 是否记录敏感数据（生产环境建议关闭）
        'log_sensitive_data' => false,
    ],
    
    // 性能配置
    'performance' => [
        // 是否启用缓存
        'cache_enabled' => true,
        
        // 缓存驱动
        'cache_driver' => 'redis',
        
        // 最大数据大小（字节）
        'max_data_size' => 1048576, // 1MB
    ],
    
    // 安全配置
    'security' => [
        // 允许的请求来源
        'allowed_origins' => [
            'localhost',
            '127.0.0.1',
            // 添加您的域名
        ],
        
        // 是否验证请求来源
        'verify_origin' => false,
        
        // 请求频率限制（每分钟）
        'rate_limit' => 100,
        
        // 是否启用IP白名单
        'ip_whitelist_enabled' => false,
        
        // IP白名单
        'ip_whitelist' => [
            '127.0.0.1',
            '::1',
        ],
    ],
    
    // 错误处理配置
    'error_handling' => [
        // 是否显示详细错误信息
        'show_details' => false,
        
        // 默认错误消息
        'default_message' => '加密处理失败',
        
        // 错误代码映射
        'error_codes' => [
            'DECRYPT_FAILED' => 4001,
            'ENCRYPT_FAILED' => 4002,
            'SIGNATURE_INVALID' => 4003,
            'REPLAY_ATTACK' => 4004,
            'KEY_INVALID' => 4005,
        ],
    ],
];
