<?php
/**
 * 同步回调处理测试脚本
 * 测试修改后的JayaPay和WatchPay同步回调处理逻辑
 */

// 数据库配置
$host = 'dianzhan_mysql';
$port = '3306';
$dbname = 'dianzhan';
$username = 'root';
$password = 'root123456';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n";
    echo "🚀 开始测试同步回调处理逻辑\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    // 测试1: JayaPay同步成功
    testJayaPaySyncSuccess($pdo);
    
    // 测试2: JayaPay同步失败
    testJayaPaySyncFailed($pdo);
    
    // 测试3: JayaPay同步待处理
    testJayaPaySyncPending($pdo);
    
    // 测试4: WatchPay同步成功
    testWatchPaySyncSuccess($pdo);
    
    // 测试5: WatchPay同步失败
    testWatchPaySyncFailed($pdo);
    
    // 测试6: WatchPay同步待处理
    testWatchPaySyncPending($pdo);
    
    // 测试7: 批量代付控制器逻辑
    testBatchWithdrawalController($pdo);
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎉 所有测试完成！\n";
    
    // 清理测试数据
    cleanupTestData($pdo);
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

/**
 * 测试JayaPay同步成功回调
 */
function testJayaPaySyncSuccess($pdo) {
    echo "\n=== 测试1: JayaPay同步成功回调 ===\n";
    
    $orderNumber = '202508041900001';
    createTestOrder($pdo, $orderNumber, '测试JayaPay同步成功', 6);
    
    // 模拟JayaPay同步成功响应
    $response = [
        'platRespCode' => 'SUCCESS',
        'platRespMessage' => 'Request success',
        'platOrderNum' => 'APF1B17D8358C804001',
        'orderNum' => $orderNumber,
        'status' => '2', // 代付成功
        'statusMsg' => 'Payout Success',
        'money' => '10000',
        'fee' => '1000'
    ];
    
    echo "📝 JayaPay响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
    echo "🔍 关键字段: status={$response['status']} (2=代付成功)\n";
    
    // 获取处理前状态
    $beforeOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理前: 订单状态={$beforeOrder['state']}, 备注={$beforeOrder['remarks']}\n";
    
    // 模拟JayaPayService::processWithdrawalDirect中的同步回调处理逻辑
    $result = processJayaPaySyncCallback($pdo, $response);
    
    // 获取处理后状态
    $afterOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理后: 订单状态={$afterOrder['state']}, 成功时间={$afterOrder['success_time']}, 备注={$afterOrder['remarks']}\n";
    echo "🔄 返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 验证结果
    if ($afterOrder['state'] == 1 && $afterOrder['success_time'] > 0) {
        echo "✅ 测试通过: 同步成功回调正确处理\n";
    } else {
        echo "❌ 测试失败: 同步成功回调处理异常\n";
    }
}

/**
 * 测试JayaPay同步失败回调
 */
function testJayaPaySyncFailed($pdo) {
    echo "\n=== 测试2: JayaPay同步失败回调 ===\n";
    
    $orderNumber = '202508041900002';
    createTestOrder($pdo, $orderNumber, '测试JayaPay同步失败', 6);
    
    // 模拟JayaPay同步失败响应
    $response = [
        'platRespCode' => 'SUCCESS',
        'platRespMessage' => 'Request success',
        'platOrderNum' => 'APF1B17D8358C804002',
        'orderNum' => $orderNumber,
        'status' => '4', // 代付失败
        'statusMsg' => 'Payout Failed',
        'money' => '10000',
        'fee' => '1000'
    ];
    
    echo "📝 JayaPay响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
    echo "🔍 关键字段: status={$response['status']} (4=代付失败)\n";
    
    // 获取处理前状态
    $beforeOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理前: 订单状态={$beforeOrder['state']}, 备注={$beforeOrder['remarks']}\n";
    
    // 模拟同步回调处理逻辑
    $result = processJayaPaySyncCallback($pdo, $response);
    
    // 获取处理后状态
    $afterOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理后: 订单状态={$afterOrder['state']}, 失败时间={$afterOrder['fail_time']}, 失败原因={$afterOrder['fail_reason']}\n";
    echo "🔄 返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 验证结果
    if ($afterOrder['state'] == 2 && $afterOrder['fail_time'] > 0) {
        echo "✅ 测试通过: 同步失败回调正确处理\n";
    } else {
        echo "❌ 测试失败: 同步失败回调处理异常\n";
    }
}

/**
 * 测试JayaPay同步待处理回调
 */
function testJayaPaySyncPending($pdo) {
    echo "\n=== 测试3: JayaPay同步待处理回调 ===\n";
    
    $orderNumber = '202508041900003';
    createTestOrder($pdo, $orderNumber, '测试JayaPay同步待处理', 6);
    
    // 模拟JayaPay同步待处理响应
    $response = [
        'platRespCode' => 'SUCCESS',
        'platRespMessage' => 'Request success',
        'platOrderNum' => 'APF1B17D8358C804003',
        'orderNum' => $orderNumber,
        'status' => '0', // 待处理
        'statusMsg' => 'Apply',
        'money' => '10000',
        'fee' => '1000'
    ];
    
    echo "📝 JayaPay响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
    echo "🔍 关键字段: status={$response['status']} (0=待处理)\n";
    
    // 获取处理前状态
    $beforeOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理前: 订单状态={$beforeOrder['state']}, 备注={$beforeOrder['remarks']}\n";
    
    // 模拟同步回调处理逻辑
    $result = processJayaPaySyncCallback($pdo, $response);
    
    // 获取处理后状态
    $afterOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理后: 订单状态={$afterOrder['state']}, 备注={$afterOrder['remarks']}\n";
    echo "🔄 返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 验证结果 - 待处理状态应该保持不变，等待异步回调
    if ($afterOrder['state'] == 4) {
        echo "✅ 测试通过: 同步待处理状态正确，等待异步回调\n";
    } else {
        echo "❌ 测试失败: 同步待处理状态处理异常\n";
    }
}

/**
 * 测试WatchPay同步成功回调
 */
function testWatchPaySyncSuccess($pdo) {
    echo "\n=== 测试4: WatchPay同步成功回调 ===\n";
    
    $orderNumber = '202508041900004';
    createTestOrder($pdo, $orderNumber, '测试WatchPay同步成功', 7);
    
    // 模拟WatchPay同步成功响应
    $response = [
        'respCode' => 'SUCCESS',
        'mchId' => '123456666',
        'merTransferId' => $orderNumber,
        'transferAmount' => '10000',
        'applyDate' => '2025-08-04 19:00:00',
        'tradeNo' => '8801029',
        'tradeResult' => '1', // 转账成功
        'errorMsg' => null
    ];
    
    echo "📝 WatchPay响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
    echo "🔍 关键字段: tradeResult={$response['tradeResult']} (1=转账成功)\n";
    
    // 获取处理前状态
    $beforeOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理前: 订单状态={$beforeOrder['state']}, 备注={$beforeOrder['remarks']}\n";
    
    // 模拟同步回调处理逻辑
    $result = processWatchPaySyncCallback($pdo, $response);
    
    // 获取处理后状态
    $afterOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理后: 订单状态={$afterOrder['state']}, 成功时间={$afterOrder['success_time']}, 备注={$afterOrder['remarks']}\n";
    echo "🔄 返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 验证结果
    if ($afterOrder['state'] == 1 && $afterOrder['success_time'] > 0) {
        echo "✅ 测试通过: WatchPay同步成功回调正确处理\n";
    } else {
        echo "❌ 测试失败: WatchPay同步成功回调处理异常\n";
    }
}

/**
 * 测试WatchPay同步失败回调
 */
function testWatchPaySyncFailed($pdo) {
    echo "\n=== 测试5: WatchPay同步失败回调 ===\n";
    
    $orderNumber = '202508041900005';
    createTestOrder($pdo, $orderNumber, '测试WatchPay同步失败', 7);
    
    // 模拟WatchPay同步失败响应
    $response = [
        'respCode' => 'SUCCESS',
        'mchId' => '123456666',
        'merTransferId' => $orderNumber,
        'transferAmount' => '10000',
        'applyDate' => '2025-08-04 19:00:00',
        'tradeNo' => '8801030',
        'tradeResult' => '2', // 转账失败
        'errorMsg' => null
    ];
    
    echo "📝 WatchPay响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
    echo "🔍 关键字段: tradeResult={$response['tradeResult']} (2=转账失败)\n";
    
    // 获取处理前状态
    $beforeOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理前: 订单状态={$beforeOrder['state']}, 备注={$beforeOrder['remarks']}\n";
    
    // 模拟同步回调处理逻辑
    $result = processWatchPaySyncCallback($pdo, $response);
    
    // 获取处理后状态
    $afterOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理后: 订单状态={$afterOrder['state']}, 失败时间={$afterOrder['fail_time']}, 失败原因={$afterOrder['fail_reason']}\n";
    echo "🔄 返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 验证结果
    if ($afterOrder['state'] == 2 && $afterOrder['fail_time'] > 0) {
        echo "✅ 测试通过: WatchPay同步失败回调正确处理\n";
    } else {
        echo "❌ 测试失败: WatchPay同步失败回调处理异常\n";
    }
}

/**
 * 测试WatchPay同步待处理回调
 */
function testWatchPaySyncPending($pdo) {
    echo "\n=== 测试6: WatchPay同步待处理回调 ===\n";
    
    $orderNumber = '202508041900006';
    createTestOrder($pdo, $orderNumber, '测试WatchPay同步待处理', 7);
    
    // 模拟WatchPay同步待处理响应
    $response = [
        'respCode' => 'SUCCESS',
        'mchId' => '123456666',
        'merTransferId' => $orderNumber,
        'transferAmount' => '10000',
        'applyDate' => '2025-08-04 19:00:00',
        'tradeNo' => '8801031',
        'tradeResult' => '0', // 申请成功
        'errorMsg' => null
    ];
    
    echo "📝 WatchPay响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
    echo "🔍 关键字段: tradeResult={$response['tradeResult']} (0=申请成功)\n";
    
    // 获取处理前状态
    $beforeOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理前: 订单状态={$beforeOrder['state']}, 备注={$beforeOrder['remarks']}\n";
    
    // 模拟同步回调处理逻辑
    $result = processWatchPaySyncCallback($pdo, $response);
    
    // 获取处理后状态
    $afterOrder = getOrderByNumber($pdo, $orderNumber);
    echo "📋 处理后: 订单状态={$afterOrder['state']}, 备注={$afterOrder['remarks']}\n";
    echo "🔄 返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 验证结果 - 待处理状态应该保持不变，等待异步回调
    if ($afterOrder['state'] == 4) {
        echo "✅ 测试通过: WatchPay同步待处理状态正确，等待异步回调\n";
    } else {
        echo "❌ 测试失败: WatchPay同步待处理状态处理异常\n";
    }
}

/**
 * 测试批量代付控制器逻辑
 */
function testBatchWithdrawalController($pdo) {
    echo "\n=== 测试7: 批量代付控制器逻辑 ===\n";

    // 测试场景1: 订单已经同步处理为成功状态
    $orderNumber1 = '202508041900007';
    createTestOrder($pdo, $orderNumber1, '测试批量代付-已同步成功', 6);

    // 先模拟订单被同步处理为成功状态
    $sql = "UPDATE ly_user_withdrawals SET state = 1, success_time = ? WHERE order_number = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([time(), $orderNumber1]);

    echo "📝 场景1: 订单已经同步处理为成功状态\n";

    // 模拟代付服务返回成功
    $result = ['code' => 1, 'msg' => '代付成功'];
    echo "🔄 代付服务返回: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";

    // 模拟BankController::batchWithdrawal中的逻辑
    $batchResult = processBatchWithdrawalLogic($pdo, $orderNumber1, $result);
    echo "📋 批量代付处理结果: {$batchResult}\n";

    // 测试场景2: 订单未同步完成，需要设置为代付中状态
    $orderNumber2 = '***************';
    createTestOrder($pdo, $orderNumber2, '测试批量代付-未同步完成', 6);

    echo "\n📝 场景2: 订单未同步完成，需要设置为代付中状态\n";

    // 模拟代付服务返回成功但未同步完成
    $result2 = ['code' => 1, 'msg' => '代付请求已提交'];
    echo "🔄 代付服务返回: " . json_encode($result2, JSON_UNESCAPED_UNICODE) . "\n";

    // 模拟批量代付处理逻辑
    $batchResult2 = processBatchWithdrawalLogic($pdo, $orderNumber2, $result2);
    echo "📋 批量代付处理结果: {$batchResult2}\n";

    // 验证结果
    $order1 = getOrderByNumber($pdo, $orderNumber1);
    $order2 = getOrderByNumber($pdo, $orderNumber2);

    echo "\n🔍 验证结果:\n";
    echo "订单1 ({$orderNumber1}): 状态={$order1['state']} (应该保持为1-成功)\n";
    echo "订单2 ({$orderNumber2}): 状态={$order2['state']} (应该更新为5-代付中)\n";

    if ($order1['state'] == 1 && $order2['state'] == 5) {
        echo "✅ 测试通过: 批量代付控制器逻辑正确处理\n";
    } else {
        echo "❌ 测试失败: 批量代付控制器逻辑处理异常\n";
    }
}

/**
 * 模拟JayaPay同步回调处理逻辑
 */
function processJayaPaySyncCallback($pdo, $response) {
    if ($response && isset($response['platRespCode']) && $response['platRespCode'] == 'SUCCESS') {
        echo "✅ JayaPay请求成功\n";

        // 处理同步回调状态
        $status = $response['status'] ?? '';
        if ($status === '2') {
            // 同步返回代付成功，立即处理成功状态
            $withdrawal = getOrderByNumber($pdo, $response['orderNum']);
            if ($withdrawal) {
                processWithdrawalSuccess($pdo, $withdrawal);
                echo "✅ 同步返回代付成功，立即处理成功状态\n";
                echo "📝 日志: JayaPay withdrawal sync success processed: {$response['orderNum']}\n";
                return ['code' => 1, 'msg' => '代付成功'];
            }
        } elseif ($status === '4') {
            // 同步返回代付失败，立即处理失败状态
            $withdrawal = getOrderByNumber($pdo, $response['orderNum']);
            if ($withdrawal) {
                processWithdrawalFailed($pdo, $withdrawal, 'JayaPay代付失败，状态：' . $status);
                echo "❌ 同步返回代付失败，立即处理失败状态\n";
                echo "📝 日志: JayaPay withdrawal sync failed processed: {$response['orderNum']}\n";
                return ['code' => 0, 'msg' => 'JayaPay代付失败，状态：' . $status];
            }
        } else {
            // 其他状态，等待异步回调
            echo "⏳ 其他状态({$status})，等待异步回调\n";
            echo "📝 日志: JayaPay withdrawal sync status {$status}, waiting for async callback: {$response['orderNum']}\n";
            return ['code' => 1, 'msg' => '代付请求已提交'];
        }
    }

    return ['code' => 0, 'msg' => '请求失败'];
}

/**
 * 模拟WatchPay同步回调处理逻辑
 */
function processWatchPaySyncCallback($pdo, $response) {
    if ($response && isset($response['respCode']) && $response['respCode'] == 'SUCCESS') {
        echo "✅ WatchPay请求成功\n";

        // 处理同步回调状态
        $tradeResult = $response['tradeResult'] ?? '';
        if ($tradeResult === '1') {
            // 同步返回转账成功，立即处理成功状态
            $withdrawal = getOrderByNumber($pdo, $response['merTransferId']);
            if ($withdrawal) {
                processWithdrawalSuccess($pdo, $withdrawal);
                echo "✅ 同步返回转账成功，立即处理成功状态\n";
                echo "📝 日志: WatchPay withdrawal sync success processed: {$response['merTransferId']}\n";
                return ['code' => 1, 'msg' => '代付成功'];
            }
        } elseif ($tradeResult === '2' || $tradeResult === '3') {
            // 同步返回转账失败或拒绝，立即处理失败状态
            $withdrawal = getOrderByNumber($pdo, $response['merTransferId']);
            if ($withdrawal) {
                $failureReason = $tradeResult === '2' ? '转账失败' : '转账拒绝';
                processWithdrawalFailed($pdo, $withdrawal, 'WatchPay代付失败：' . $failureReason);
                echo "❌ 同步返回{$failureReason}，立即处理失败状态\n";
                echo "📝 日志: WatchPay withdrawal sync failed processed: {$response['merTransferId']}, reason: {$failureReason}\n";
                return ['code' => 0, 'msg' => 'WatchPay代付失败：' . $failureReason];
            }
        } else {
            // 其他状态，等待异步回调
            echo "⏳ 其他状态({$tradeResult})，等待异步回调\n";
            echo "📝 日志: WatchPay withdrawal sync status {$tradeResult}, waiting for async callback: {$response['merTransferId']}\n";
            return ['code' => 1, 'msg' => '代付请求已提交'];
        }
    }

    return ['code' => 0, 'msg' => '请求失败'];
}

/**
 * 模拟批量代付控制器处理逻辑
 */
function processBatchWithdrawalLogic($pdo, $orderNumber, $result) {
    if ($result['code'] == 1) {
        // 检查是否已经同步处理完成（JayaPay/WatchPay可能同步返回最终状态）
        $currentOrder = getOrderByNumber($pdo, $orderNumber);

        if ($currentOrder['state'] == 1) {
            // 订单已经同步处理为成功状态
            echo "✅ 订单已经同步处理为成功状态\n";
            echo "📝 日志: 批量代付订单ID:{$currentOrder['id']}（订单号：{$orderNumber}，金额：￥{$currentOrder['price']}，渠道：JayaPay代付APP）- 同步成功\n";
            return "批量代付同步成功";
        } else {
            // 代付请求成功但未同步完成，更新状态为代付中
            $sql = "UPDATE ly_user_withdrawals SET state = 5, process_time = ? WHERE order_number = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([time(), $orderNumber]);

            echo "⏳ 代付请求成功但未同步完成，更新状态为代付中\n";
            echo "📝 日志: 批量代付订单ID:{$currentOrder['id']}（订单号：{$orderNumber}，金额：￥{$currentOrder['price']}，渠道：JayaPay代付APP）\n";
            return "批量代付成功";
        }
    }

    return "批量代付失败";
}

/**
 * 创建测试订单
 */
function createTestOrder($pdo, $orderNumber, $remarks, $channelId = 6) {
    $sql = "INSERT INTO ly_user_withdrawals (uid, order_number, bank_id, bank_name, card_number, card_name, price, time, state, channel_id, remarks)
            VALUES (1150, ?, '19', 'DANA', '************', 'SAPRUDIN', 10000.0000, ?, 4, ?, ?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$orderNumber, time(), $channelId, $remarks]);
    echo "📋 创建测试订单: {$orderNumber} (渠道ID: {$channelId})\n";
}

/**
 * 根据订单号获取订单信息
 */
function getOrderByNumber($pdo, $orderNumber) {
    $sql = "SELECT * FROM ly_user_withdrawals WHERE order_number = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$orderNumber]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * 处理提现成功
 */
function processWithdrawalSuccess($pdo, $withdrawal) {
    $sql = "UPDATE ly_user_withdrawals SET state = 1, success_time = ?, remarks = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([time(), '同步代付成功', $withdrawal['id']]);
}

/**
 * 处理提现失败
 */
function processWithdrawalFailed($pdo, $withdrawal, $reason) {
    $sql = "UPDATE ly_user_withdrawals SET state = 2, fail_time = ?, fail_reason = ?, remarks = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([time(), $reason, '同步代付失败', $withdrawal['id']]);
}

/**
 * 清理测试数据
 */
function cleanupTestData($pdo) {
    $sql = "DELETE FROM ly_user_withdrawals WHERE order_number LIKE '202508041900%'";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    echo "🧹 测试数据已清理\n";
}
